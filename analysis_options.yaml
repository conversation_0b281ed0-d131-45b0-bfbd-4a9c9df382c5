include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Prefer const constructors
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    prefer_const_declarations: true
    
    # Code quality
    avoid_print: true
    prefer_final_fields: true
    prefer_final_locals: true
    unnecessary_null_checks: true
    unnecessary_null_in_if_null_operators: true
    
    # Performance
    avoid_function_literals_in_foreach_calls: true
    prefer_for_elements_to_map_fromIterable: true
    
    # Style
    require_trailing_commas: true
    sort_child_properties_last: true
    use_key_in_widget_constructors: true
    
    # Error prevention
    always_use_package_imports: true
    avoid_relative_lib_imports: true
