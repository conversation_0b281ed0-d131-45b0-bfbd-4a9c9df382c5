-- OpenFit Database Schema
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  age INTEGER,
  gender TEXT CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
  height_cm INTEGER,
  weight_kg DECIMAL(5,2),
  fitness_level TEXT CHECK (fitness_level IN ('beginner', 'intermediate', 'advanced')),
  goals TEXT[], -- Array of goals like ['lose_weight', 'build_muscle', 'improve_cardio']
  preferred_workout_days TEXT[], -- Array like ['monday', 'wednesday', 'friday']
  preferred_workout_time TEXT, -- 'morning', 'afternoon', 'evening'
  onboarding_completed BOOLEAN DEFAULT FALSE,
  has_completed_preferences BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create exercises table
CREATE TABLE IF NOT EXISTS exercises (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  instructions TEXT[],
  category TEXT NOT NULL, -- 'strength', 'cardio', 'flexibility', 'balance'
  equipment TEXT, -- 'none', 'dumbbells', 'barbell', 'resistance_band', etc.
  primary_muscle TEXT,
  secondary_muscles TEXT[],
  difficulty TEXT CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
  image_url TEXT,
  video_url TEXT,
  duration_seconds INTEGER, -- For timed exercises
  reps_range TEXT, -- e.g., "8-12" for strength exercises
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workouts table
CREATE TABLE IF NOT EXISTS workouts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  difficulty TEXT CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
  estimated_duration INTEGER, -- in minutes
  equipment_needed TEXT[],
  target_muscles TEXT[],
  image_url TEXT,
  is_public BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workout_exercises junction table
CREATE TABLE IF NOT EXISTS workout_exercises (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workout_id UUID REFERENCES workouts(id) ON DELETE CASCADE,
  exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
  order_index INTEGER NOT NULL,
  sets INTEGER,
  reps INTEGER,
  duration_seconds INTEGER,
  rest_seconds INTEGER,
  weight_kg DECIMAL(5,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workout_sessions table (completed workouts)
CREATE TABLE IF NOT EXISTS workout_sessions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  workout_id UUID REFERENCES workouts(id),
  started_at TIMESTAMP WITH TIME ZONE NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER,
  calories_burned INTEGER,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  notes TEXT,
  is_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create session_exercises table (individual exercise performance)
CREATE TABLE IF NOT EXISTS session_exercises (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  session_id UUID REFERENCES workout_sessions(id) ON DELETE CASCADE,
  exercise_id UUID REFERENCES exercises(id),
  sets_completed INTEGER,
  reps_completed INTEGER,
  weight_used_kg DECIMAL(5,2),
  duration_seconds INTEGER,
  rest_seconds INTEGER,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_goals table
CREATE TABLE IF NOT EXISTS user_goals (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  goal_type TEXT NOT NULL, -- 'weight_loss', 'muscle_gain', 'strength', 'endurance', etc.
  target_value DECIMAL(10,2),
  current_value DECIMAL(10,2) DEFAULT 0,
  unit TEXT, -- 'kg', 'lbs', 'minutes', 'reps', etc.
  target_date DATE,
  is_achieved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create progress table for tracking various metrics
CREATE TABLE IF NOT EXISTS progress (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  metric_type TEXT NOT NULL, -- 'weight', 'body_fat', 'muscle_mass', 'workout_streak', etc.
  value DECIMAL(10,2) NOT NULL,
  unit TEXT,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT
);

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE workouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_exercises ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Workouts policies (public workouts viewable by all, private by owner only)
CREATE POLICY "Public workouts are viewable by everyone" ON workouts FOR SELECT USING (is_public = true OR created_by = auth.uid());
CREATE POLICY "Users can create workouts" ON workouts FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own workouts" ON workouts FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own workouts" ON workouts FOR DELETE USING (created_by = auth.uid());

-- Workout sessions policies
CREATE POLICY "Users can view own workout sessions" ON workout_sessions FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own workout sessions" ON workout_sessions FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own workout sessions" ON workout_sessions FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Users can delete own workout sessions" ON workout_sessions FOR DELETE USING (user_id = auth.uid());

-- Session exercises policies
CREATE POLICY "Users can view own session exercises" ON session_exercises 
  FOR SELECT USING (session_id IN (SELECT id FROM workout_sessions WHERE user_id = auth.uid()));
CREATE POLICY "Users can create own session exercises" ON session_exercises 
  FOR INSERT WITH CHECK (session_id IN (SELECT id FROM workout_sessions WHERE user_id = auth.uid()));
CREATE POLICY "Users can update own session exercises" ON session_exercises 
  FOR UPDATE USING (session_id IN (SELECT id FROM workout_sessions WHERE user_id = auth.uid()));
CREATE POLICY "Users can delete own session exercises" ON session_exercises 
  FOR DELETE USING (session_id IN (SELECT id FROM workout_sessions WHERE user_id = auth.uid()));

-- User goals policies
CREATE POLICY "Users can view own goals" ON user_goals FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own goals" ON user_goals FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own goals" ON user_goals FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Users can delete own goals" ON user_goals FOR DELETE USING (user_id = auth.uid());

-- Progress policies
CREATE POLICY "Users can view own progress" ON progress FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own progress" ON progress FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own progress" ON progress FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Users can delete own progress" ON progress FOR DELETE USING (user_id = auth.uid());

-- Exercises are public (no RLS needed for now)
-- Users can read all exercises, only admins can modify

-- Functions and triggers

-- Function to handle updated_at timestamps
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION handle_updated_at();
CREATE TRIGGER workouts_updated_at BEFORE UPDATE ON workouts FOR EACH ROW EXECUTE FUNCTION handle_updated_at();
CREATE TRIGGER user_goals_updated_at BEFORE UPDATE ON user_goals FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Function to create profile on user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Insert some sample exercises
INSERT INTO exercises (name, description, category, equipment, primary_muscle, difficulty, instructions, reps_range) VALUES
('Push-ups', 'Classic bodyweight chest exercise', 'strength', 'none', 'chest', 'beginner', 
 ARRAY['Start in plank position', 'Lower body until chest nearly touches floor', 'Push back up to starting position'], '8-15'),
('Squats', 'Fundamental lower body exercise', 'strength', 'none', 'quadriceps', 'beginner',
 ARRAY['Stand with feet shoulder-width apart', 'Lower body as if sitting back into chair', 'Return to standing position'], '10-20'),
('Plank', 'Core strengthening exercise', 'strength', 'none', 'core', 'beginner',
 ARRAY['Start in push-up position', 'Hold body in straight line', 'Engage core muscles'], '30-60 seconds'),
('Jumping Jacks', 'Full body cardio exercise', 'cardio', 'none', 'full_body', 'beginner',
 ARRAY['Start with feet together, arms at sides', 'Jump while spreading legs and raising arms overhead', 'Return to starting position'], '20-30'),
('Burpees', 'High-intensity full body exercise', 'cardio', 'none', 'full_body', 'intermediate',
 ARRAY['Start standing', 'Drop into squat, place hands on floor', 'Jump feet back to plank', 'Do push-up', 'Jump feet forward', 'Jump up with arms overhead'], '5-15');

-- Insert some sample workouts
INSERT INTO workouts (name, description, category, difficulty, estimated_duration, equipment_needed, target_muscles) VALUES
('Beginner Full Body', 'Perfect starter workout for beginners', 'full_body', 'beginner', 20, ARRAY['none'], ARRAY['chest', 'legs', 'core']),
('HIIT Cardio Blast', 'High intensity interval training', 'cardio', 'intermediate', 15, ARRAY['none'], ARRAY['full_body']),
('Core Strength', 'Focus on building core stability', 'core', 'beginner', 10, ARRAY['none'], ARRAY['core', 'abs']);

-- Link exercises to workouts (you'll need to get the actual UUIDs after insertion)
-- This is just an example structure - you'll need to populate with actual IDs