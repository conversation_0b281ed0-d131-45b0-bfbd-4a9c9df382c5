# Design Document

## Overview

This feature adds a development-only testing mechanism to easily reset and test the onboarding flow. It integrates seamlessly with the existing profile screen and authentication system.

## Architecture

### Components
- **Debug Menu Item**: Added to profile screen menu list
- **Reset Service**: Method to reset onboarding status in database
- **Navigation Handler**: Automatic navigation to onboarding flow after reset
- **Development Flag**: Controls visibility of debug features

### Flow Diagram
```
Profile Screen → Test Onboarding Button → Confirmation Dialog → Reset Database → Navigate to Onboarding → Complete Flow → Return to Main App
```

## Components and Interfaces

### 1. Profile Screen Enhancement
- Add conditional menu item for "Test Onboarding"
- Only visible when `SupabaseConfig.isDevelopment = true`
- Styled consistently with other menu items
- Shows debug icon to indicate development feature

### 2. Reset Onboarding Service
```dart
class ProfileNotifier {
  Future<void> resetOnboardingForTesting() async {
    // Reset only onboarding flags, preserve other data
    await updateProfileFields({
      'onboarding_completed': false,
      'has_completed_preferences': false,
    });
  }
}
```

### 3. Confirmation Dialog
- Clear warning about what will happen
- Emphasizes this is for testing only
- Cancel and Confirm options
- Shows development warning styling

### 4. Navigation Integration
- Leverages existing AuthWrapper logic
- When onboarding_completed = false, automatically shows OnboardingFlow
- No additional navigation code needed

## Data Models

### Database Changes
- No schema changes required
- Only updates existing boolean flags:
  - `onboarding_completed`: false
  - `has_completed_preferences`: false

### State Management
- Uses existing ProfileNotifier
- Triggers AuthWrapper re-evaluation
- Preserves all other profile data

## Error Handling

### Reset Failures
- Network errors: Show retry option
- Database errors: Display error message
- Authentication errors: Redirect to sign in

### Edge Cases
- User cancels during reset: No changes made
- App closed during reset: Safe, idempotent operation
- Multiple rapid taps: Debounced to prevent duplicate requests

## Testing Strategy

### Manual Testing
1. Verify button only appears in development mode
2. Test reset functionality with existing user data
3. Verify onboarding flow works after reset
4. Confirm data preservation during reset
5. Test error scenarios (network issues, etc.)

### Automated Testing
- Unit tests for reset service method
- Widget tests for conditional button visibility
- Integration tests for full reset → onboarding → completion flow

## Implementation Notes

### Development Flag Check
```dart
if (SupabaseConfig.isDevelopment) {
  // Show test onboarding option
}
```

### Menu Item Styling
- Use debug-specific icon (🧪 or ⚙️)
- Different color to indicate development feature
- Clear labeling: "Test Onboarding (Dev Only)"

### User Experience
- Immediate feedback on button press
- Clear progress indication during reset
- Smooth transition to onboarding flow
- Preserved data shows in onboarding forms

## Security Considerations

- Feature only available in development builds
- No production impact
- Preserves user authentication
- Safe database operations (only updates flags)

## Performance Impact

- Minimal: Single database update operation
- No additional memory usage
- Leverages existing navigation system
- Fast execution (< 1 second typical)