# Implementation Plan

- [x] 1. Add reset onboarding method to ProfileNotifier
  - Create resetOnboardingForTesting() method in enhanced_auth_providers.dart
  - Method should only update onboarding flags, preserve other data
  - Add proper error handling and loading states
  - _Requirements: 1.3, 1.4, 3.1, 3.2, 4.1, 4.2, 4.3_

- [x] 2. Add development-only menu item to profile screen
  - Add conditional menu item in profile_screen.dart menu list
  - Only show when SupabaseConfig.isDevelopment is true
  - Style with debug icon and appropriate colors
  - Position logically in menu (near other debug options)
  - _Requirements: 1.1, 2.1, 2.2, 2.3_

- [x] 3. Implement confirmation dialog for onboarding reset
  - Create confirmation dialog with clear warning message
  - Explain what will happen (reset onboarding, preserve data)
  - Add Cancel and Confirm buttons
  - Style as development/debug dialog
  - _Requirements: 1.2, 3.3, 4.4_

- [x] 4. Connect reset functionality to navigation flow
  - Call resetOnboardingForTesting() when user confirms
  - Handle loading state during reset operation
  - Show success/error feedback to user
  - Rely on <PERSON>th<PERSON><PERSON><PERSON> to automatically navigate to onboarding
  - _Requirements: 1.4, 1.5, 4.1, 4.2, 4.3, 4.4_

- [x] 5. Test the complete flow end-to-end
  - Verify button only appears in development mode
  - Test reset with existing user data
  - Confirm data is preserved and pre-populated in onboarding
  - Test error scenarios and recovery
  - Verify smooth navigation back to main app after completion
  - _Requirements: All requirements_