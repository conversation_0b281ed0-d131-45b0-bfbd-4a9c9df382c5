# Design Document

## Overview

This redesign transforms the onboarding experience into a modern, comprehensive questionnaire system that captures detailed user information through an engaging, movemate-inspired interface. The flow consists of multiple themed sections with various question types and adaptive logic.

## Architecture

### Flow Structure
```
Welcome → Personal Info → Fitness Goals → Experience Level → Workout Preferences → Equipment & Schedule → Health & Limitations → Summary & Completion
```

### Question Types
- **Single Choice**: Modern card selection with visual feedback
- **Multiple Choice**: Multi-select cards with checkmarks
- **Range Input**: Interactive sliders with live preview
- **Text Input**: Styled fields with validation
- **Rating**: Star or scale-based rating systems
- **Numeric**: Steppers and number inputs
- **Date/Time**: Modern picker interfaces

## Components and Interfaces

### 1. Enhanced Onboarding Flow Manager
```dart
class EnhancedOnboardingFlow extends ConsumerStatefulWidget {
  // Manages overall flow state and navigation
  // Handles data persistence and validation
  // Coordinates between different question screens
}
```

### 2. Question Screen Base Class
```dart
abstract class OnboardingQuestionScreen extends ConsumerWidget {
  // Base class for all question screens
  // Provides common UI elements and navigation
  // Handles data saving and validation
}
```

### 3. Modern Question Widgets
```dart
class ModernChoiceCard extends StatelessWidget {
  // Glassmorphic selection cards
  // Hover effects and animations
  // Multi-select support
}

class ModernSlider extends StatefulWidget {
  // Custom slider with movemate styling
  // Live value display
  // Range validation
}

class ModernTextInput extends StatefulWidget {
  // Styled text input with floating labels
  // Validation feedback
  // Character counting
}
```

### 4. Progress System
```dart
class OnboardingProgress extends StatelessWidget {
  // Animated progress bar
  // Step indicators
  // Completion percentage
}
```

## Data Models

### Enhanced Onboarding Data
```dart
class EnhancedOnboardingData {
  // Personal Information
  String? fullName;
  int? age;
  Gender? gender;
  double? height;
  HeightUnit? heightUnit;
  double? weight;
  WeightUnit? weightUnit;
  
  // Fitness Goals (Priority Ranked)
  List<FitnessGoal> primaryGoals;
  List<FitnessGoal> secondaryGoals;
  String? specificGoalDetails;
  
  // Experience & Fitness Level
  FitnessExperience? overallExperience;
  int? cardioLevel; // 1-10 scale
  int? strengthLevel; // 1-10 scale
  int? flexibilityLevel; // 1-10 scale
  List<String> previousActivities;
  
  // Workout Preferences
  List<WorkoutType> preferredWorkoutTypes;
  List<DayOfWeek> availableDays;
  TimeOfDay? preferredWorkoutTime;
  int? sessionDuration; // minutes
  int? weeklyFrequency;
  IntensityPreference? intensityPreference;
  
  // Equipment & Environment
  WorkoutLocation? primaryLocation;
  List<Equipment> availableEquipment;
  double? budgetForEquipment;
  
  // Health & Limitations
  List<HealthCondition> healthConditions;
  List<PhysicalLimitation> physicalLimitations;
  List<Exercise> exercisesToAvoid;
  String? additionalNotes;
  
  // Motivation & Tracking
  List<MotivationFactor> motivationFactors;
  TrackingPreference? trackingPreference;
  bool? wantsReminders;
  List<TimeOfDay> reminderTimes;
}
```

## Screen-by-Screen Design

### 1. Welcome Screen
- **Hero animation** with app logo and tagline
- **"Let's Get Started"** call-to-action
- **Progress indicator**: 0/8 steps
- **Skip option** for returning users

### 2. Personal Information (Step 1/8)
- **Name input** with floating label
- **Age selector** with modern stepper
- **Gender selection** with inclusive options
- **Height/Weight inputs** with unit toggles
- **Progress**: Animated to 12.5%

### 3. Fitness Goals (Step 2/8)
- **Primary goal selection** (single choice cards)
  - Weight Loss, Muscle Gain, Endurance, Strength, Flexibility, General Health
- **Secondary goals** (multi-select)
- **Specific target input** (optional text field)
- **Goal timeline** (dropdown)
- **Progress**: 25%

### 4. Experience Level (Step 3/8)
- **Overall fitness experience** (beginner/intermediate/advanced cards)
- **Activity-specific levels** (sliders for cardio/strength/flexibility)
- **Previous activities** (multi-select with search)
- **Workout frequency history** (slider)
- **Progress**: 37.5%

### 5. Workout Preferences (Step 4/8)
- **Preferred workout types** (multi-select cards with icons)
- **Available days** (weekly calendar selector)
- **Preferred time** (time picker with visual clock)
- **Session duration** (slider with time visualization)
- **Intensity preference** (scale selector)
- **Progress**: 50%

### 6. Equipment & Schedule (Step 5/8)
- **Primary workout location** (home/gym/outdoor/mixed)
- **Available equipment** (multi-select with images)
- **Equipment budget** (optional slider)
- **Schedule flexibility** (rating scale)
- **Progress**: 62.5%

### 7. Health & Limitations (Step 6/8)
- **Health conditions** (multi-select with "None" option)
- **Physical limitations** (multi-select with descriptions)
- **Exercises to avoid** (searchable multi-select)
- **Additional notes** (optional text area)
- **Progress**: 75%

### 8. Motivation & Tracking (Step 7/8)
- **Motivation factors** (rank top 3 from list)
- **Tracking preferences** (detailed/simple/minimal)
- **Reminder preferences** (toggle + time selection)
- **Social features** (sharing/challenges toggle)
- **Progress**: 87.5%

### 9. Summary & Completion (Step 8/8)
- **Profile summary** with key information
- **Personalized welcome message**
- **"Start Your Journey"** completion button
- **Edit options** for each section
- **Progress**: 100% with celebration animation

## Visual Design System

### Color Palette (Movemate-inspired)
- **Primary**: #FF6B47 (Orange accent)
- **Background**: #1A1D2B (Deep navy)
- **Cards**: #2A2D3A (Dark cards)
- **Text Primary**: #FFFFFF
- **Text Secondary**: #9CA3AF
- **Success**: #4ADE80
- **Warning**: #FBBF24

### Typography
- **Headers**: Inter Bold, 24-28px
- **Subheaders**: Inter SemiBold, 18-20px
- **Body**: Inter Regular, 14-16px
- **Captions**: Inter Regular, 12px

### Components
- **Cards**: 16px border radius, subtle shadows
- **Buttons**: 12px border radius, smooth transitions
- **Inputs**: Floating labels, focus animations
- **Sliders**: Custom thumb design, gradient tracks
- **Progress**: Animated with smooth transitions

## Animations & Transitions

### Screen Transitions
- **Forward**: Slide left with fade
- **Backward**: Slide right with fade
- **Duration**: 300ms with easeInOut curve

### Component Animations
- **Card selection**: Scale + glow effect
- **Progress updates**: Smooth bar animation
- **Input focus**: Label float + border glow
- **Completion**: Confetti + success animation

## Data Persistence Strategy

### Auto-save System
- **Save on screen completion**: Automatic background save
- **Save on input change**: Debounced saves for text inputs
- **Resume capability**: Load saved state on app restart
- **Validation**: Client-side validation with server sync

### Database Schema Updates
- **Enhanced profile fields**: Add new comprehensive fields
- **Migration strategy**: Backward compatible updates
- **Data validation**: Server-side validation rules
- **Privacy compliance**: Secure handling of health data

## Accessibility Features

### Screen Reader Support
- **Semantic labels**: Proper accessibility labels
- **Navigation hints**: Clear instructions for each step
- **Progress announcements**: Verbal progress updates
- **Error descriptions**: Clear error explanations

### Visual Accessibility
- **High contrast mode**: Alternative color scheme
- **Large text support**: Scalable font sizes
- **Color blind friendly**: Icons + text for all selections
- **Focus indicators**: Clear keyboard navigation

## Performance Considerations

### Optimization Strategies
- **Lazy loading**: Load screens as needed
- **Image optimization**: Compressed assets with caching
- **Animation performance**: 60fps smooth animations
- **Memory management**: Efficient state management
- **Network efficiency**: Batched data saves

### Loading States
- **Skeleton screens**: While loading saved data
- **Progress indicators**: For save operations
- **Offline support**: Continue onboarding without network
- **Error recovery**: Graceful handling of failures