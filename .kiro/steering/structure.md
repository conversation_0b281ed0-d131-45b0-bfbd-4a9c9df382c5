# OpenFit - Project Structure

## Root Directory Structure
```
openfit/
├── lib/                    # Main application code
├── android/               # Android-specific configuration
├── ios/                   # iOS-specific configuration  
├── web/                   # Web platform assets
├── pubspec.yaml          # Dependencies and project config
└── analysis_options.yaml # Linting configuration
```

## Core Application Structure (`lib/`)
```
lib/
├── main.dart             # App entry point
├── theme.dart           # Material 3 theme definitions
├── data/                # Sample data and data sources
├── models/              # Data models and entities
├── screens/             # UI screens and pages
└── widgets/             # Reusable UI components
```

## Key Directories

### `/lib/models/`
Data models representing core entities:
- `user.dart` - User profile and preferences
- `workout.dart` - Workout templates with exercises
- `exercise.dart` - Individual exercise definitions
- `workout_session.dart` - Completed workout instances
- `auth_user.dart` - Authentication user data
- `onboarding_data.dart` - Onboarding flow data

### `/lib/screens/`
Main application screens organized by feature:
- `main_screen.dart` - Bottom navigation wrapper
- `home_screen.dart` - Dashboard with stats
- `workouts_screen.dart` - Workout library
- `workout_detail_screen.dart` - Exercise details
- `active_workout_screen.dart` - Live workout tracking
- `workout_timer_screen.dart` - Exercise timer
- `progress_screen.dart` - Analytics and charts
- `profile_screen.dart` - User settings
- `auth/` - Authentication screens
- `onboarding/` - User setup flow

### `/lib/widgets/`
Reusable UI components:
- `stat_card.dart` - Metric display cards
- `workout_card.dart` - Workout preview cards
- `top_knob_bar.dart` - Custom app bar
- `auth_button.dart` - Authentication buttons
- `auth_text_field.dart` - Form input fields
- `onboarding/` - Onboarding-specific widgets

### `/lib/data/`
Data management:
- `sample_data.dart` - Mock data for development
- `workout_data.dart` - Workout-related data operations

## Naming Conventions
- **Files**: snake_case (e.g., `workout_detail_screen.dart`)
- **Classes**: PascalCase (e.g., `WorkoutDetailScreen`)
- **Variables/Functions**: camelCase (e.g., `workoutDuration`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_WORKOUT_TIME`)

## Screen Organization Pattern
Each screen follows this structure:
1. Imports (Flutter, packages, local files)
2. StatefulWidget/StatelessWidget class
3. State class (if stateful)
4. Build method with clear widget hierarchy
5. Helper methods and event handlers

## Widget Organization
- Keep widgets focused and single-purpose
- Extract reusable components to `/lib/widgets/`
- Use descriptive names that indicate widget purpose
- Group related widgets in subdirectories when needed

## Theme Usage
- Access theme colors via `Theme.of(context).colorScheme`
- Use predefined text styles from theme
- Leverage Material 3 design tokens
- Custom colors defined in `theme.dart` for fitness-specific branding