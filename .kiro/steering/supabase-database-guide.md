# Supabase Database Query Guide for OpenFit

## Project Configuration
- **Project ID**: `xtazgqpcaujwwaswzeoh` (SciWell Mobile)
- **Project URL**: `https://xtazgqpcaujwwaswzeoh.supabase.co`
- **Environment**: Development (isDevelopment = true)

## Database Tables Overview

### 1. `profiles` Table (User Profile Data)
**Purpose**: Stores user profile information including personal details, fitness preferences, and onboarding data.

**Key Columns for Onboarding**:
- `id` (uuid) - Primary key, matches auth.users.id
- `email` (text) - User email
- `display_name` (text) - User's full name
- `gender` (text) - User's gender
- `age` (integer) - User's age
- `height` (numeric) - User's height value
- `height_unit` (text) - Height unit (default: 'cm')
- `weight` (numeric) - User's weight value  
- `weight_unit` (text) - Weight unit (default: 'kg')
- `onboarding_completed` (boolean) - Whether user completed onboarding
- `has_completed_preferences` (boolean) - Whether user completed preferences

**Important**: The profiles table uses different column names than our UserProfile model expects:
- Model expects: `fullName` → Database has: `display_name`
- Model expects: `heightCm` → Database has: `height` + `height_unit`
- Model expects: `weightKg` → Database has: `weight` + `weight_unit`

### 2. `exercises` Table
**Purpose**: Contains exercise definitions with instructions and metadata.

**Key Columns**:
- `id` (uuid) - Primary key
- `name` (text) - Exercise name
- `description` (text) - Exercise description
- `instructions` (text) - How to perform the exercise
- `primary_muscle` (text) - Primary muscle group
- `secondary_muscle` (text) - Secondary muscle groups
- `equipment` (text) - Required equipment
- `category` (text) - Exercise category
- `video_url` (text) - Video demonstration URL
- `vertical_video` (text) - Vertical video URL

### 3. `workouts` Table
**Purpose**: User-created workout sessions.

**Key Columns**:
- `id` (uuid) - Primary key
- `user_id` (uuid) - Foreign key to profiles.id
- `name` (text) - Workout name
- `is_active` (boolean) - Whether workout is currently active
- `is_completed` (boolean) - Whether workout is completed
- `start_time` (timestamptz) - When workout started
- `end_time` (timestamptz) - When workout ended
- `duration` (integer) - Calculated duration in seconds
- `notes` (text) - User notes
- `ai_description` (text) - AI-generated description

### 4. `workout_exercises` Table
**Purpose**: Links exercises to workouts with set/rep information.

**Key Columns**:
- `id` (uuid) - Primary key
- `workout_id` (uuid) - Foreign key to workouts.id
- `exercise_id` (uuid) - Foreign key to exercises.id
- `name` (text) - Exercise name (denormalized)
- `sets` (integer) - Number of sets
- `reps` (array of bigint) - Reps for each set
- `weight` (array of bigint) - Weight for each set
- `order_index` (integer) - Exercise order in workout
- `rest_interval` (bigint) - Rest time between sets
- `completed` (boolean) - Whether exercise is completed

## Query Patterns

### 1. Get User Profile
```sql
SELECT * FROM profiles WHERE id = $user_id;
```

### 2. Update User Profile (Upsert)
```sql
INSERT INTO profiles (id, email, display_name, gender, age, height, height_unit, weight, weight_unit, updated_at)
VALUES ($user_id, $email, $display_name, $gender, $age, $height, $height_unit, $weight, $weight_unit, NOW())
ON CONFLICT (id) 
DO UPDATE SET 
  display_name = EXCLUDED.display_name,
  gender = EXCLUDED.gender,
  age = EXCLUDED.age,
  height = EXCLUDED.height,
  height_unit = EXCLUDED.height_unit,
  weight = EXCLUDED.weight,
  weight_unit = EXCLUDED.weight_unit,
  updated_at = NOW();
```

### 3. Get User Workouts with Exercises
```sql
SELECT w.*, 
       json_agg(
         json_build_object(
           'id', we.id,
           'exercise_id', we.exercise_id,
           'name', we.name,
           'sets', we.sets,
           'reps', we.reps,
           'weight', we.weight,
           'order_index', we.order_index,
           'exercise_details', e.*
         ) ORDER BY we.order_index
       ) as exercises
FROM workouts w
LEFT JOIN workout_exercises we ON w.id = we.workout_id
LEFT JOIN exercises e ON we.exercise_id = e.id
WHERE w.user_id = $user_id
GROUP BY w.id
ORDER BY w.created_at DESC;
```

### 4. Search Exercises
```sql
SELECT * FROM exercises 
WHERE name ILIKE '%' || $search_term || '%'
   OR description ILIKE '%' || $search_term || '%'
ORDER BY name
LIMIT 20;
```

## Field Mapping Issues to Fix

### UserProfile Model vs Database Schema
Our `UserProfile` model expects different field names than what's in the database:

**Current Model Fields** → **Database Columns**:
- `fullName` → `display_name`
- `heightCm` → `height` (with `height_unit`)
- `weightKg` → `weight` (with `weight_unit`)
- `goals` → `fitness_goals_array` or `primarygoal`
- `preferredWorkoutDays` → `workout_days` or `workoutdays`
- `preferredWorkoutTime` → `preferred_workout_duration`

### Required Code Changes
1. **Update UserProfile.fromJson()** to map database column names correctly
2. **Update UserProfile.toJson()** to use correct database column names
3. **Fix onboarding data loading** to use correct field names
4. **Update Supabase service** to handle field mapping

## Authentication Flow
1. User signs up/in through Supabase Auth
2. User ID is stored in `auth.users` table
3. Profile data is stored in `profiles` table with same ID
4. RLS (Row Level Security) ensures users can only access their own data

## Common Queries for Development

### Check if user has profile data:
```sql
SELECT display_name, gender, age, height, weight, onboarding_completed 
FROM profiles 
WHERE id = $user_id;
```

### Get user's active workout:
```sql
SELECT * FROM workouts 
WHERE user_id = $user_id AND is_active = true 
ORDER BY created_at DESC 
LIMIT 1;
```

### Get exercise categories:
```sql
SELECT DISTINCT category FROM exercises 
WHERE category IS NOT NULL 
ORDER BY category;
```

## Debugging Tips
1. **Check user authentication**: Verify `currentUser?.id` exists
2. **Check profile exists**: Query profiles table directly
3. **Verify field names**: Database uses snake_case, model uses camelCase
4. **Check RLS policies**: Ensure user can read/write their own data
5. **Use MCP tools**: Query database directly to verify data structure

## Next Steps
1. Fix field mapping in UserProfile model
2. Update onboarding screens to use correct field names
3. Test profile data loading and saving
4. Verify all CRUD operations work correctly