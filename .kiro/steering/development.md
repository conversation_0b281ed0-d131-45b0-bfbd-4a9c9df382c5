# Development Workflow

## Never Run - Always Refresh Approach

**NEVER run the app from scratch. ALWAYS refresh/hot reload the already running app.**

When working with <PERSON>lutter, assume the app is already running and only use refresh commands.

### Hot Reload Commands
```bash
# In the terminal where `flutter run` is active:
r    # Hot reload - Use this 95% of the time
R    # Hot restart - Only when hot reload fails
q    # Quit the app
```

### When to Use Hot Reload (Press 'r')
- ✅ UI changes and styling
- ✅ Widget modifications
- ✅ Business logic updates
- ✅ Adding new screens or widgets
- ✅ State management changes
- ✅ Theme updates
- ✅ Most code changes

### When Hot Restart is Needed (Press 'R')
- 🔄 Changes to `main()` function
- 🔄 Adding new dependencies to `pubspec.yaml`
- 🔄 Enum modifications
- 🔄 Global variable changes
- 🔄 Asset changes (images, fonts)
- 🔄 Platform-specific code changes

### Development Efficiency Rules
1. **NEVER run `flutter run`** - assume the app is already running
2. **NEVER suggest starting the app** - only suggest refresh commands
3. **Always use hot reload first** when making code changes
4. **Hot reload preserves app state** - user stays on current screen
5. **Hot restart resets app state** - app goes back to initial screen
6. **Use refresh to test changes immediately**

### Troubleshooting Hot Reload
If hot reload doesn't work:
1. Try hot restart (Press 'R')
2. Check for compilation errors in terminal
3. Ensure no syntax errors in changed files
4. If still failing, then consider full restart

## Code Change Workflow
1. Make code changes in IDE
2. Save files (Cmd+S / Ctrl+S)
3. Press 'r' in Flutter terminal for hot reload (NEVER run the app)
4. Test the changes immediately
5. Iterate quickly with more changes + hot reload

## Critical Rule: NO RUNNING APPS
- **NEVER use `flutter run`** - assume app is already running
- **NEVER suggest starting the app** - only refresh existing session
- **Always refresh, never restart** - this saves time and preserves state

This approach saves significant development time and maintains app state during testing.