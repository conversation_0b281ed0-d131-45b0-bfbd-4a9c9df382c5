# OpenFit - Technical Stack

## Framework & Language
- **Flutter 3.6+** - Cross-platform mobile development framework
- **Dart** - Primary programming language
- **Material Design 3** - UI design system with custom fitness-focused theme

## Key Dependencies
- `google_fonts: ^6.1.0` - Inter font family for consistent typography
- `shared_preferences: ^2.0.0` - Local data persistence for user settings and workout data
- `fl_chart: 0.68.0` - Chart library for progress visualization
- `cupertino_icons: ^1.0.8` - iOS-style icons

## Development Tools
- `flutter_lints: ^5.0.0` - Dart/Flutter linting rules
- `flutter_test` - Testing framework

## Common Commands

### Development
```bash
# Run the app in debug mode
flutter run

# Run on specific device
flutter run -d <device-id>

# Hot reload during development (PREFERRED METHOD)
# Press 'r' in terminal or save files in IDE
# Use this for most code changes - it's instant!

# Hot restart (when hot reload doesn't work)
# Press 'R' in terminal
# Use when changing main(), adding new dependencies, or major structural changes
```

### Hot Reload Best Practices
- **Always try hot reload first** - Press 'r' in the terminal running `flutter run`
- **Hot reload works for**: UI changes, business logic, styling, new widgets
- **Hot restart needed for**: main() changes, new dependencies, enum changes, global variables
- **If app is already running, NEVER restart it** - just hot reload to see changes
- **Save time**: Hot reload takes ~1 second vs full restart taking 10-30 seconds

### Building
```bash
# Build APK for Android
flutter build apk

# Build iOS (requires macOS and Xcode)
flutter build ios

# Build for web
flutter build web
```

### Testing & Analysis
```bash
# Run all tests
flutter test

# Analyze code for issues
flutter analyze

# Check dependencies
flutter pub deps

# Update dependencies
flutter pub upgrade
```

### Project Setup
```bash
# Get dependencies after cloning
flutter pub get

# Clean build artifacts
flutter clean

# Rebuild after clean
flutter pub get && flutter run
```

## Architecture Notes
- Uses Material 3 theming with custom color schemes
- Local data storage via SharedPreferences (no external database)
- Sample data provided for development and testing
- Follows standard Flutter project structure with clear separation of concerns