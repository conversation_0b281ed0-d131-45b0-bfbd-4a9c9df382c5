import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// Custom exception types for the app
abstract class AppException implements Exception {
  const AppException(this.message, [this.prefix]);
  
  final String message;
  final String? prefix;
  
  @override
  String toString() => '${prefix ?? 'AppException'}: $message';
}

class NetworkException extends AppException {
  const NetworkException(String message) : super(message, 'Network Error');
}

class ValidationException extends AppException {
  const ValidationException(String message) : super(message, 'Validation Error');
}

class StorageException extends AppException {
  const StorageException(String message) : super(message, 'Storage Error');
}

class WorkoutException extends AppException {
  const WorkoutException(String message) : super(message, 'Workout Error');
}

/// Global error handler for the application
class ErrorHandler {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.none,
    ),
  );

  /// Handle and log errors
  static void handleError(
    Object error,
    StackTrace stackTrace, {
    String? context,
    bool showToUser = true,
  }) {
    // Log the error
    _logError(error, stackTrace, context);
    
    // Show user-friendly message if needed
    if (showToUser && !kDebugMode) {
      _showUserError(error);
    }
  }

  /// Log error with appropriate level
  static void _logError(Object error, StackTrace stackTrace, String? context) {
    final contextInfo = context != null ? '[$context] ' : '';
    
    if (error is AppException) {
      _logger.w('${contextInfo}App Exception: ${error.toString()}');
    } else if (error is FlutterError) {
      _logger.e(
        '${contextInfo}Flutter Error: ${error.toString()}', 
        error: error, 
        stackTrace: stackTrace,
      );
    } else {
      _logger.e(
        '${contextInfo}Unexpected Error: ${error.toString()}', 
        error: error, 
        stackTrace: stackTrace,
      );
    }
  }

  /// Show user-friendly error message
  static void _showUserError(Object error) {
    // This would typically show a snackbar or dialog
    // For now, we'll just log it
    final userMessage = _getUserFriendlyMessage(error);
    _logger.i('User message: $userMessage');
  }

  /// Convert technical errors to user-friendly messages
  static String _getUserFriendlyMessage(Object error) {
    if (error is NetworkException) {
      return 'Please check your internet connection and try again.';
    } else if (error is ValidationException) {
      return error.message;
    } else if (error is StorageException) {
      return 'Unable to save your data. Please try again.';
    } else if (error is WorkoutException) {
      return 'There was an issue with your workout. Please try again.';
    } else {
      return 'Something went wrong. Please try again later.';
    }
  }

  /// Wrapper for async operations with error handling
  static Future<T?> safeAsyncCall<T>(
    Future<T> Function() operation, {
    String? context,
    T? fallback,
    bool showError = true,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace, context: context, showToUser: showError);
      return fallback;
    }
  }

  /// Wrapper for sync operations with error handling
  static T? safeSyncCall<T>(
    T Function() operation, {
    String? context,
    T? fallback,
    bool showError = true,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      handleError(error, stackTrace, context: context, showToUser: showError);
      return fallback;
    }
  }
}

/// Error boundary widget for catching widget build errors
class ErrorBoundary extends StatelessWidget {
  const ErrorBoundary({
    required this.child,
    this.fallback,
    super.key,
  });

  final Widget child;
  final Widget? fallback;

  @override
  Widget build(BuildContext context) {
    return child;
  }

  static Widget defaultFallback(BuildContext context, Object error) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Oops! Something went wrong',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'We\'re working to fix this issue. Please try again later.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}