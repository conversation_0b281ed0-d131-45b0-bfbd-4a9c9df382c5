import 'package:openfit/models/exercise.dart';
import 'package:openfit/models/workout.dart';
import 'package:openfit/models/workout_session.dart';
import 'package:openfit/models/user.dart';

class SampleData {
  static final List<Exercise> _exercises = [
    Exercise(
      id: 'e1',
      name: 'Push-ups',
      description: 'Classic bodyweight chest exercise',
      category: 'Chest',
      difficulty: 'Beginner',
      duration: 60,
      restTime: 30,
      instructions: [
        'Start in plank position',
        'Lower your body until chest nearly touches floor',
        'Push back up to starting position',
        'Keep your core tight throughout'
      ],
      muscleGroups: 'Chest, Shoulders, Triceps',
    ),
    Exercise(
      id: 'e2',
      name: 'Squats',
      description: 'Fundamental lower body exercise',
      category: 'Legs',
      difficulty: 'Beginner',
      duration: 45,
      restTime: 30,
      instructions: [
        'Stand with feet shoulder-width apart',
        'Lower your body as if sitting in a chair',
        'Keep your knees behind your toes',
        'Push through heels to return to start'
      ],
      muscleGroups: 'Quadriceps, Glutes, Hamstrings',
    ),
    Exercise(
      id: 'e3',
      name: 'Plank',
      description: 'Core strengthening isometric hold',
      category: 'Core',
      difficulty: 'Beginner',
      duration: 60,
      restTime: 30,
      instructions: [
        'Start in forearm plank position',
        'Keep your body in straight line',
        'Engage core muscles',
        'Hold position without sagging hips'
      ],
      muscleGroups: 'Core, Shoulders',
    ),
    Exercise(
      id: 'e4',
      name: 'Burpees',
      description: 'Full-body high intensity exercise',
      category: 'Cardio',
      difficulty: 'Intermediate',
      duration: 45,
      restTime: 45,
      instructions: [
        'Start in standing position',
        'Drop into squat and place hands on floor',
        'Jump feet back into plank',
        'Jump feet forward and jump up with arms overhead'
      ],
      muscleGroups: 'Full Body',
    ),
    Exercise(
      id: 'e5',
      name: 'Mountain Climbers',
      description: 'Dynamic cardio and core exercise',
      category: 'Cardio',
      difficulty: 'Intermediate',
      duration: 30,
      restTime: 30,
      instructions: [
        'Start in high plank position',
        'Alternate bringing knees to chest',
        'Keep hips level and core engaged',
        'Maintain steady rhythm'
      ],
      muscleGroups: 'Core, Cardio',
    ),
    Exercise(
      id: 'e6',
      name: 'Lunges',
      description: 'Unilateral leg strengthening exercise',
      category: 'Legs',
      difficulty: 'Beginner',
      duration: 60,
      restTime: 30,
      instructions: [
        'Step forward with one leg',
        'Lower hips until both knees are 90 degrees',
        'Push back to starting position',
        'Repeat on other side'
      ],
      muscleGroups: 'Quadriceps, Glutes, Hamstrings',
    ),
  ];

  static final List<Workout> workouts = [
    Workout(
      id: 'w1',
      name: 'Quick Morning Boost',
      description: 'Start your day with energy',
      category: 'Beginner',
      difficulty: 'Beginner',
      estimatedDuration: 15,
      exercises: [_exercises[0], _exercises[1], _exercises[2]],
      caloriesBurn: 120,
    ),
    Workout(
      id: 'w2',
      name: 'HIIT Cardio Blast',
      description: 'High intensity fat burning workout',
      category: 'Cardio',
      difficulty: 'Intermediate',
      estimatedDuration: 20,
      exercises: [_exercises[3], _exercises[4], _exercises[0], _exercises[1]],
      caloriesBurn: 250,
    ),
    Workout(
      id: 'w3',
      name: 'Core Strength Builder',
      description: 'Build a strong and stable core',
      category: 'Core',
      difficulty: 'Beginner',
      estimatedDuration: 12,
      exercises: [_exercises[2], _exercises[4], _exercises[0]],
      caloriesBurn: 100,
    ),
    Workout(
      id: 'w4',
      name: 'Lower Body Power',
      description: 'Strengthen and tone your legs',
      category: 'Strength',
      difficulty: 'Intermediate',
      estimatedDuration: 25,
      exercises: [_exercises[1], _exercises[5], _exercises[3]],
      caloriesBurn: 200,
    ),
    Workout(
      id: 'w5',
      name: 'Full Body Challenge',
      description: 'Complete workout for all muscle groups',
      category: 'Full Body',
      difficulty: 'Advanced',
      estimatedDuration: 35,
      exercises: _exercises,
      caloriesBurn: 350,
    ),
  ];

  static final List<WorkoutSession> recentSessions = [
    WorkoutSession(
      id: 's1',
      workoutId: 'w1',
      workoutName: 'Quick Morning Boost',
      workoutType: 'Beginner',
      imageUrl: '',
      startTime: DateTime.now().subtract(const Duration(days: 1)),
      endTime: DateTime.now().subtract(const Duration(days: 1)).add(const Duration(minutes: 16)),
      totalDuration: const Duration(minutes: 15),
      currentDuration: const Duration(minutes: 16),
      exercises: [
        ExerciseSession(
          id: 'es1',
          exerciseName: 'Push-ups',
          description: 'Classic bodyweight chest exercise',
          duration: const Duration(seconds: 60),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'es2',
          exerciseName: 'Squats',
          description: 'Fundamental lower body exercise',
          duration: const Duration(seconds: 45),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'es3',
          exerciseName: 'Plank',
          description: 'Core strengthening isometric hold',
          duration: const Duration(seconds: 60),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 125,
      completionPercentage: 100.0,
      rating: 4.5,
    ),
    WorkoutSession(
      id: 's2',
      workoutId: 'w2',
      workoutName: 'HIIT Cardio Blast',
      workoutType: 'Cardio',
      imageUrl: '',
      startTime: DateTime.now().subtract(const Duration(days: 2)),
      endTime: DateTime.now().subtract(const Duration(days: 2)).add(const Duration(minutes: 22)),
      totalDuration: const Duration(minutes: 20),
      currentDuration: const Duration(minutes: 22),
      exercises: [
        ExerciseSession(
          id: 'es4',
          exerciseName: 'Burpees',
          description: 'Full-body high intensity exercise',
          duration: const Duration(seconds: 45),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'es5',
          exerciseName: 'Mountain Climbers',
          description: 'Dynamic cardio and core exercise',
          duration: const Duration(seconds: 30),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 260,
      completionPercentage: 100.0,
      rating: 5.0,
    ),
    WorkoutSession(
      id: 's3',
      workoutId: 'w3',
      workoutName: 'Core Strength Builder',
      workoutType: 'Core',
      imageUrl: '',
      startTime: DateTime.now().subtract(const Duration(days: 4)),
      endTime: DateTime.now().subtract(const Duration(days: 4)).add(const Duration(minutes: 14)),
      totalDuration: const Duration(minutes: 12),
      currentDuration: const Duration(minutes: 14),
      exercises: [
        ExerciseSession(
          id: 'es6',
          exerciseName: 'Plank',
          description: 'Core strengthening isometric hold',
          duration: const Duration(seconds: 60),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 110,
      completionPercentage: 100.0,
      rating: 4.0,
    ),
  ];

  static final User sampleUser = User(
    id: 'u1',
    name: 'Alex Johnson',
    email: '<EMAIL>',
    age: 28,
    weight: 70.0,
    height: 175.0,
    fitnessGoal: 'Weight Loss',
    weeklyWorkoutGoal: 4,
  );

  static List<Exercise> get exercises => _exercises;
}