import 'package:openfit/models/workout_session.dart';

class WorkoutData {
  static final List<WorkoutSession> sampleWorkouts = [
    // Strength Training Workout
    WorkoutSession(
      id: 'workout_1',
      workoutId: 'strength_1',
      workoutName: 'Strength',
      workoutType: 'Strength Training',
      imageUrl: 'https://pixabay.com/get/g169d983ede8c390d5d8cd9d0d639045ac8e7e09b3607d565e95c6f2223378a5fa48e508a21d7ebe02daafb987bcf3c20d2d7586057d48fbd480cf4a0d7bd9e79_1280.jpg',
      startTime: DateTime.now().subtract(const Duration(hours: 2)),
      endTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 15)),
      totalDuration: const Duration(minutes: 45),
      currentDuration: const Duration(minutes: 45),
      exercises: [
        ExerciseSession(
          id: 'ex_1',
          exerciseName: 'Push-ups',
          description: 'Standard push-ups for upper body strength',
          duration: const Duration(minutes: 3),
          sets: 3,
          reps: 15,
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'ex_2',
          exerciseName: 'Squats',
          description: 'Bodyweight squats for leg strength',
          duration: const Duration(minutes: 3),
          sets: 3,
          reps: 20,
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 320,
      completionPercentage: 100.0,
      rating: 4.5,
    ),

    // Cardio & HIIT Workout
    WorkoutSession(
      id: 'workout_2',
      workoutId: 'cardio_1',
      workoutName: 'Cardio & Core',
      workoutType: 'Cardio',
      imageUrl: 'https://pixabay.com/get/g574d2a51a64ac72a76aad5a24fee702695ccbf2a6c00ba02b94f77546b3ece61bdb87fb872d6d493478fc86366874a4ac0208e4c2c60886fc20f066e8379b72a_1280.jpg',
      startTime: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      endTime: DateTime.now().subtract(const Duration(days: 1, hours: 2, minutes: 35)),
      totalDuration: const Duration(minutes: 30),
      currentDuration: const Duration(minutes: 25),
      exercises: [
        ExerciseSession(
          id: 'ex_3',
          exerciseName: 'Jumping Jacks',
          description: 'High-intensity cardio exercise',
          duration: const Duration(minutes: 2),
          sets: 3,
          reps: 30,
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'ex_4',
          exerciseName: 'Mountain Climbers',
          description: 'Core and cardio combination',
          duration: const Duration(minutes: 2),
          sets: 3,
          reps: 20,
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 245,
      completionPercentage: 83.3,
      rating: 4.2,
    ),

    // Yoga Workout
    WorkoutSession(
      id: 'workout_3',
      workoutId: 'yoga_1',
      workoutName: 'Yoga',
      workoutType: 'Yoga & Mindfulness',
      imageUrl: 'https://pixabay.com/get/gbd90464fe709e2c0ab07ba714434602ccb3a135b6ae04cf25d2b1a0487d1a5989ff49ceb4d7a7d288305400d79468f04d2b8b0e2ff0179a1ef8842a39da8a892_1280.jpg',
      startTime: DateTime.now().subtract(const Duration(days: 2)),
      endTime: DateTime.now().subtract(const Duration(days: 2, minutes: -20)),
      totalDuration: const Duration(minutes: 25),
      currentDuration: const Duration(minutes: 20),
      exercises: [
        ExerciseSession(
          id: 'ex_5',
          exerciseName: 'Sun Salutation',
          description: 'Traditional yoga flow sequence',
          duration: const Duration(minutes: 8),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
        ExerciseSession(
          id: 'ex_6',
          exerciseName: 'Warrior Poses',
          description: 'Strength and balance poses',
          duration: const Duration(minutes: 7),
          isCompleted: true,
          videoUrl: '',
          thumbnailUrl: '',
        ),
      ],
      status: WorkoutSessionStatus.completed,
      caloriesBurned: 180,
      completionPercentage: 80.0,
      rating: 4.8,
    ),
  ];

  static final List<WorkoutHistoryEntry> sampleHistory = [
    WorkoutHistoryEntry(
      id: 'hist_1',
      workoutName: 'Strength',
      workoutType: 'Strength Training',
      date: DateTime.now().subtract(const Duration(hours: 2)),
      duration: const Duration(minutes: 45),
      caloriesBurned: 320,
      completionPercentage: 100.0,
      imageUrl: 'https://pixabay.com/get/g169d983ede8c390d5d8cd9d0d639045ac8e7e09b3607d565e95c6f2223378a5fa48e508a21d7ebe02daafb987bcf3c20d2d7586057d48fbd480cf4a0d7bd9e79_1280.jpg',
      exercisesCompleted: ['Push-ups', 'Squats', 'Planks'],
    ),
    WorkoutHistoryEntry(
      id: 'hist_2',
      workoutName: 'Cardio & Core',
      workoutType: 'Cardio',
      date: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      duration: const Duration(minutes: 25),
      caloriesBurned: 245,
      completionPercentage: 83.3,
      imageUrl: 'https://pixabay.com/get/g574d2a51a64ac72a76aad5a24fee702695ccbf2a6c00ba02b94f77546b3ece61bdb87fb872d6d493478fc86366874a4ac0208e4c2c60886fc20f066e8379b72a_1280.jpg',
      exercisesCompleted: ['Jumping Jacks', 'Mountain Climbers'],
    ),
    WorkoutHistoryEntry(
      id: 'hist_3',
      workoutName: 'Yoga',
      workoutType: 'Yoga & Mindfulness',
      date: DateTime.now().subtract(const Duration(days: 2)),
      duration: const Duration(minutes: 20),
      caloriesBurned: 180,
      completionPercentage: 80.0,
      imageUrl: 'https://pixabay.com/get/gbd90464fe709e2c0ab07ba714434602ccb3a135b6ae04cf25d2b1a0487d1a5989ff49ceb4d7a7d288305400d79468f04d2b8b0e2ff0179a1ef8842a39da8a892_1280.jpg',
      exercisesCompleted: ['Sun Salutation', 'Warrior Poses'],
    ),
    WorkoutHistoryEntry(
      id: 'hist_4',
      workoutName: 'Stamina',
      workoutType: 'Cardio',
      date: DateTime.now().subtract(const Duration(days: 3)),
      duration: const Duration(minutes: 30),
      caloriesBurned: 290,
      completionPercentage: 100.0,
      imageUrl: 'https://pixabay.com/get/g574d2a51a64ac72a76aad5a24fee702695ccbf2a6c00ba02b94f77546b3ece61bdb87fb872d6d493478fc86366874a4ac0208e4c2c60886fc20f066e8379b72a_1280.jpg',
      exercisesCompleted: ['Running', 'Burpees', 'High Knees'],
    ),
    WorkoutHistoryEntry(
      id: 'hist_5',
      workoutName: 'Strength',
      workoutType: 'Strength Training',
      date: DateTime.now().subtract(const Duration(days: 4)),
      duration: const Duration(minutes: 50),
      caloriesBurned: 380,
      completionPercentage: 95.0,
      imageUrl: 'https://pixabay.com/get/g169d983ede8c390d5d8cd9d0d639045ac8e7e09b3607d565e95c6f2223378a5fa48e508a21d7ebe02daafb987bcf3c20d2d7586057d48fbd480cf4a0d7bd9e79_1280.jpg',
      exercisesCompleted: ['Deadlifts', 'Bench Press', 'Pull-ups'],
    ),
  ];

  // Warm-up workout for active session (as shown in design)
  static WorkoutSession get warmUpWorkout => WorkoutSession(
    id: 'warmup_active',
    workoutId: 'warmup_1',
    workoutName: 'Warm-Up',
    workoutType: 'Warm-Up',
    imageUrl: 'https://pixabay.com/get/gd983aed0c1c9d5e2a416c7669b29ba90d385d19f0741d981220852975f4bcd0301c0b679372a839554b72baa07c9e482839f91af062806dd84d8558faea0e733_1280.jpg',
    startTime: DateTime.now().subtract(const Duration(minutes: 7, seconds: 45)),
    totalDuration: const Duration(minutes: 10),
    currentDuration: const Duration(minutes: 7, seconds: 45),
    exercises: [
      ExerciseSession(
        id: 'warmup_1',
        exerciseName: 'Arm Circles',
        description: 'Gentle arm warming exercise',
        duration: const Duration(minutes: 2),
        isCompleted: true,
        videoUrl: '',
        thumbnailUrl: '',
      ),
      ExerciseSession(
        id: 'warmup_2',
        exerciseName: 'Leg Swings',
        description: 'Dynamic leg stretching',
        duration: const Duration(minutes: 3),
        isCompleted: true,
        videoUrl: '',
        thumbnailUrl: '',
      ),
      ExerciseSession(
        id: 'warmup_3',
        exerciseName: 'Torso Twists',
        description: 'Core and spine mobility',
        duration: const Duration(minutes: 5),
        isCompleted: false,
        videoUrl: '',
        thumbnailUrl: '',
      ),
    ],
    status: WorkoutSessionStatus.inProgress,
    caloriesBurned: 85,
    completionPercentage: 77.5,
    rating: 0.0,
  );
}