import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfit/core/error_handler.dart';
import 'package:openfit/models/supabase_exercise.dart';
import 'package:openfit/services/supabase_service_simple.dart';

/// Supabase service provider
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

/// Authentication state provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  return Supabase.instance.client.auth.onAuthStateChange;
});

/// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.session?.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

/// User profile provider (placeholder for now)
final userProfileProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  // TODO: Implement user profile loading
  return null;
});

/// Exercises provider with filtering
final exercisesProvider = FutureProvider.family<List<SupabaseExercise>, ExerciseFilters>((ref, filters) async {
  final service = ref.read(supabaseServiceProvider);
  
  return await ErrorHandler.safeAsyncCall(
    () => service.getExercises(
      limit: filters.limit,
      category: filters.category,
      equipment: filters.equipment,
      primaryMuscle: filters.primaryMuscle,
    ),
    context: 'Loading exercises',
    fallback: <SupabaseExercise>[],
  ) ?? <SupabaseExercise>[];
});

/// All exercises provider (no filters)
final allExercisesProvider = FutureProvider<List<SupabaseExercise>>((ref) async {
  final result = await ref.watch(exercisesProvider(const ExerciseFilters()).future);
  return result;
});

/// Exercise search provider
final exerciseSearchProvider = FutureProvider.family<List<SupabaseExercise>, String>((ref, query) async {
  if (query.isEmpty) return [];
  
  final service = ref.read(supabaseServiceProvider);
  return await ErrorHandler.safeAsyncCall(
    () => service.searchExercises(query),
    context: 'Searching exercises',
    fallback: <SupabaseExercise>[],
  ) ?? <SupabaseExercise>[];
});

/// Exercise categories provider
final exerciseCategoriesProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.read(supabaseServiceProvider);
  return await ErrorHandler.safeAsyncCall(
    () => service.getExerciseCategories(),
    context: 'Loading exercise categories',
    fallback: <String>[],
  ) ?? <String>[];
});

/// Equipment types provider
final equipmentTypesProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.read(supabaseServiceProvider);
  return await ErrorHandler.safeAsyncCall(
    () => service.getEquipmentTypes(),
    context: 'Loading equipment types',
    fallback: <String>[],
  ) ?? <String>[];
});

/// Primary muscles provider
final primaryMusclesProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.read(supabaseServiceProvider);
  return await ErrorHandler.safeAsyncCall(
    () => service.getPrimaryMuscles(),
    context: 'Loading primary muscles',
    fallback: <String>[],
  ) ?? <String>[];
});

/// User workouts provider (placeholder)
final userWorkoutsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  // TODO: Implement user workouts loading
  return <Map<String, dynamic>>[];
});

/// Completed workouts provider for statistics (placeholder)
final completedWorkoutsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  // TODO: Implement completed workouts loading
  return <Map<String, dynamic>>[];
});

/// Workout statistics provider
final workoutStatsProvider = Provider<WorkoutStatistics>((ref) {
  final completedWorkoutsAsync = ref.watch(completedWorkoutsProvider);
  
  return completedWorkoutsAsync.when(
    data: (workouts) => WorkoutStatistics.fromCompletedWorkouts(workouts),
    loading: () => const WorkoutStatistics(),
    error: (_, __) => const WorkoutStatistics(),
  );
});

/// Profile update notifier (placeholder)
final profileUpdateProvider = StateNotifierProvider<ProfileUpdateNotifier, AsyncValue<Map<String, dynamic>?>>((ref) {
  return ProfileUpdateNotifier();
});

class ProfileUpdateNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>?>> {
  ProfileUpdateNotifier() : super(const AsyncValue.data(null));

  Future<void> updateProfile(Map<String, dynamic> profile) async {
    state = const AsyncValue.loading();
    try {
      // TODO: Implement profile update
      state = AsyncValue.data(profile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Exercise filters data class
class ExerciseFilters {
  final int? limit;
  final String? category;
  final String? equipment;
  final String? primaryMuscle;

  const ExerciseFilters({
    this.limit,
    this.category,
    this.equipment,
    this.primaryMuscle,
  });

  ExerciseFilters copyWith({
    int? limit,
    String? category,
    String? equipment,
    String? primaryMuscle,
  }) {
    return ExerciseFilters(
      limit: limit ?? this.limit,
      category: category ?? this.category,
      equipment: equipment ?? this.equipment,
      primaryMuscle: primaryMuscle ?? this.primaryMuscle,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExerciseFilters &&
        other.limit == limit &&
        other.category == category &&
        other.equipment == equipment &&
        other.primaryMuscle == primaryMuscle;
  }

  @override
  int get hashCode {
    return limit.hashCode ^
        category.hashCode ^
        equipment.hashCode ^
        primaryMuscle.hashCode;
  }
}

/// Workout statistics data class
class WorkoutStatistics {
  final int totalWorkouts;
  final int totalMinutes;
  final int totalCalories;
  final int currentStreak;
  final double averageRating;
  final int workoutsThisWeek;
  final int workoutsThisMonth;

  const WorkoutStatistics({
    this.totalWorkouts = 0,
    this.totalMinutes = 0,
    this.totalCalories = 0,
    this.currentStreak = 0,
    this.averageRating = 0.0,
    this.workoutsThisWeek = 0,
    this.workoutsThisMonth = 0,
  });

  factory WorkoutStatistics.fromCompletedWorkouts(List<Map<String, dynamic>> workouts) {
    if (workouts.isEmpty) return const WorkoutStatistics();

    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final monthStart = DateTime(now.year, now.month, 1);

    int totalWorkouts = workouts.length;
    int totalMinutes = 0;
    int totalCalories = 0;
    double totalRating = 0;
    int ratedWorkouts = 0;
    int workoutsThisWeek = 0;
    int workoutsThisMonth = 0;

    for (final workout in workouts) {
      // Duration
      final duration = workout['duration'] as int? ?? 0;
      totalMinutes += duration;

      // Calories
      final calories = workout['calories_burned'] as int? ?? 0;
      totalCalories += calories;

      // Rating
      final rating = workout['rating'] as int?;
      if (rating != null) {
        totalRating += rating.toDouble();
        ratedWorkouts++;
      }

      // Date-based counts
      final dateCompleted = workout['date_completed'] as String?;
      if (dateCompleted != null) {
        final date = DateTime.parse(dateCompleted);
        if (date.isAfter(weekStart)) workoutsThisWeek++;
        if (date.isAfter(monthStart)) workoutsThisMonth++;
      }
    }

    // Calculate streak (simplified - would need more complex logic for real streak)
    int currentStreak = 0;
    if (workouts.isNotEmpty) {
      final lastWorkout = workouts.first['date_completed'] as String?;
      if (lastWorkout != null) {
        final lastDate = DateTime.parse(lastWorkout);
        final daysSinceLastWorkout = now.difference(lastDate).inDays;
        if (daysSinceLastWorkout <= 1) {
          currentStreak = 1; // Simplified streak calculation
        }
      }
    }

    return WorkoutStatistics(
      totalWorkouts: totalWorkouts,
      totalMinutes: totalMinutes,
      totalCalories: totalCalories,
      currentStreak: currentStreak,
      averageRating: ratedWorkouts > 0 ? totalRating / ratedWorkouts : 0.0,
      workoutsThisWeek: workoutsThisWeek,
      workoutsThisMonth: workoutsThisMonth,
    );
  }
}