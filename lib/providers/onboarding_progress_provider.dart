import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';

// Onboarding step definitions
enum OnboardingStep {
  welcome(0, 'Welcome'),
  personalInfo(1, 'Personal Information'),
  fitnessGoals(2, 'Fitness Goals'),
  experienceLevel(3, 'Experience Level'),
  workoutPreferences(4, 'Workout Preferences'),
  equipment(5, 'Equipment & Schedule'),
  health(6, 'Health & Limitations'),
  motivation(7, 'Motivation & Tracking'),
  summary(8, 'Summary'),
  completed(9, 'Completed');

  const OnboardingStep(this.stepIndex, this.title);
  
  final int stepIndex;
  final String title;
  
  static OnboardingStep fromIndex(int stepIndex) {
    return OnboardingStep.values.firstWhere(
      (step) => step.stepIndex == stepIndex,
      orElse: () => OnboardingStep.welcome,
    );
  }
  
  OnboardingStep get next {
    if (stepIndex < OnboardingStep.values.length - 1) {
      return OnboardingStep.fromIndex(stepIndex + 1);
    }
    return this;
  }
  
  OnboardingStep get previous {
    if (stepIndex > 0) {
      return OnboardingStep.fromIndex(stepIndex - 1);
    }
    return this;
  }
  
  bool get isCompleted => stepIndex >= OnboardingStep.completed.stepIndex;
  
  static int get totalSteps => OnboardingStep.values.length - 1; // Exclude completed
}

// Progress state class
class OnboardingProgressState {
  final OnboardingStep currentStep;
  final Map<String, dynamic> stepData;
  final bool isLoading;
  final String? error;
  final DateTime? lastSaved;

  const OnboardingProgressState({
    this.currentStep = OnboardingStep.welcome,
    this.stepData = const {},
    this.isLoading = false,
    this.error,
    this.lastSaved,
  });

  OnboardingProgressState copyWith({
    OnboardingStep? currentStep,
    Map<String, dynamic>? stepData,
    bool? isLoading,
    String? error,
    DateTime? lastSaved,
  }) {
    return OnboardingProgressState(
      currentStep: currentStep ?? this.currentStep,
      stepData: stepData ?? this.stepData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastSaved: lastSaved ?? this.lastSaved,
    );
  }

  double get progressPercentage {
    return (currentStep.stepIndex / OnboardingStep.totalSteps * 100).clamp(0, 100);
  }

  bool get isCompleted => currentStep.isCompleted;

  Map<String, dynamic> toJson() {
    return {
      'currentStep': currentStep.stepIndex,
      'stepData': stepData,
      'lastSaved': lastSaved?.toIso8601String(),
    };
  }

  factory OnboardingProgressState.fromJson(Map<String, dynamic> json) {
    return OnboardingProgressState(
      currentStep: OnboardingStep.fromIndex(json['currentStep'] ?? 0),
      stepData: Map<String, dynamic>.from(json['stepData'] ?? {}),
      lastSaved: json['lastSaved'] != null 
          ? DateTime.parse(json['lastSaved'])
          : null,
    );
  }
}

// Progress notifier
class OnboardingProgressNotifier extends StateNotifier<OnboardingProgressState> {
  OnboardingProgressNotifier() : super(const OnboardingProgressState()) {
    _loadProgress();
  }

  static const String _storageKey = 'onboarding_progress_v2';

  // Load progress from storage
  Future<void> _loadProgress() async {
    try {
      state = state.copyWith(isLoading: true);
      
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString != null) {
        final json = jsonDecode(jsonString);
        state = OnboardingProgressState.fromJson(json);
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load progress: $e',
        isLoading: false,
      );
    } finally {
      if (state.isLoading) {
        state = state.copyWith(isLoading: false);
      }
    }
  }

  // Save progress to storage
  Future<void> _saveProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final updatedState = state.copyWith(lastSaved: DateTime.now());
      
      await prefs.setString(_storageKey, jsonEncode(updatedState.toJson()));
      state = updatedState;
    } catch (e) {
      state = state.copyWith(error: 'Failed to save progress: $e');
    }
  }

  // Navigate to next step
  Future<void> nextStep() async {
    if (!state.currentStep.isCompleted) {
      state = state.copyWith(
        currentStep: state.currentStep.next,
        error: null,
      );
      await _saveProgress();
    }
  }

  // Navigate to previous step
  Future<void> previousStep() async {
    if (state.currentStep.stepIndex > 0) {
      state = state.copyWith(
        currentStep: state.currentStep.previous,
        error: null,
      );
      await _saveProgress();
    }
  }

  // Jump to specific step
  Future<void> goToStep(OnboardingStep step) async {
    state = state.copyWith(
      currentStep: step,
      error: null,
    );
    await _saveProgress();
  }

  // Update step data
  Future<void> updateStepData(String key, dynamic value) async {
    final updatedData = Map<String, dynamic>.from(state.stepData);
    updatedData[key] = value;
    
    state = state.copyWith(
      stepData: updatedData,
      error: null,
    );
    
    // Auto-save with debouncing
    _debouncedSave();
  }

  // Update multiple step data at once
  Future<void> updateMultipleStepData(Map<String, dynamic> data) async {
    final updatedData = Map<String, dynamic>.from(state.stepData);
    updatedData.addAll(data);
    
    state = state.copyWith(
      stepData: updatedData,
      error: null,
    );
    
    await _saveProgress();
  }

  // Complete onboarding
  Future<void> completeOnboarding() async {
    state = state.copyWith(
      currentStep: OnboardingStep.completed,
      error: null,
    );
    await _saveProgress();
  }

  // Reset progress
  Future<void> resetProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      
      state = const OnboardingProgressState();
    } catch (e) {
      state = state.copyWith(error: 'Failed to reset progress: $e');
    }
  }

  // Get data for specific step
  T? getStepData<T>(String key) {
    return state.stepData[key] as T?;
  }

  // Check if step has required data
  bool hasRequiredData(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.personalInfo:
        return state.stepData.containsKey('fullName') &&
               state.stepData.containsKey('age') &&
               state.stepData.containsKey('gender');
      case OnboardingStep.fitnessGoals:
        return state.stepData.containsKey('fitnessGoals') &&
               (state.stepData['fitnessGoals'] as List?)?.isNotEmpty == true;
      case OnboardingStep.experienceLevel:
        return state.stepData.containsKey('experienceLevel');
      case OnboardingStep.workoutPreferences:
        return state.stepData.containsKey('workoutDays') &&
               state.stepData.containsKey('workoutDuration');
      default:
        return true; // Other steps are optional or have no required data
    }
  }

  // Debounced save to avoid too frequent saves
  Timer? _saveTimer;
  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), () {
      _saveProgress();
    });
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }
}

// Provider
final onboardingProgressProvider = StateNotifierProvider<OnboardingProgressNotifier, OnboardingProgressState>(
  (ref) => OnboardingProgressNotifier(),
);

// Convenience providers
final currentOnboardingStepProvider = Provider<OnboardingStep>((ref) {
  return ref.watch(onboardingProgressProvider).currentStep;
});

final onboardingProgressPercentageProvider = Provider<double>((ref) {
  return ref.watch(onboardingProgressProvider).progressPercentage;
});

final isOnboardingCompletedProvider = Provider<bool>((ref) {
  return ref.watch(onboardingProgressProvider).isCompleted;
});

final onboardingStepDataProvider = Provider<Map<String, dynamic>>((ref) {
  return ref.watch(onboardingProgressProvider).stepData;
});

// Helper to get step titles for progress display
final onboardingStepTitlesProvider = Provider<List<String>>((ref) {
  return OnboardingStep.values
      .where((step) => step != OnboardingStep.completed)
      .map((step) => step.title)
      .toList();
});