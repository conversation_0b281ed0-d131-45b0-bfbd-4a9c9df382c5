import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/models/enhanced_workout.dart';
import 'package:openfit/models/workout_session_enhanced.dart';
import 'package:openfit/providers/enhanced_auth_providers.dart';

/// Workout-related providers for managing workouts, exercises, and sessions

/// Available workouts provider
final workoutsProvider = FutureProvider<List<EnhancedWorkout>>((ref) async {
  try {
    // For now, return sample workouts - will be replaced with real Supabase data
    return _getSampleWorkouts();
  } catch (e) {
    return [];
  }
});

/// Workout by ID provider
final workoutByIdProvider = FutureProvider.family<EnhancedWorkout?, String>((ref, workoutId) async {
  try {
    // For now, return sample workout - will be replaced with real Supabase data
    final workouts = _getSampleWorkouts();
    return workouts.firstWhere((w) => w.id == workoutId);
  } catch (e) {
    return null;
  }
});

/// Available exercises provider
final exercisesProvider = FutureProvider<List<EnhancedExercise>>((ref) async {
  try {
    // For now, return sample exercises - will be replaced with real Supabase data
    return _getSampleExercises();
  } catch (e) {
    return [];
  }
});

/// User's workout sessions provider
final workoutSessionsProvider = FutureProvider<List<WorkoutSessionEnhanced>>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return [];

  try {
    // For now, return sample sessions - will be replaced with real Supabase data
    return _getSampleSessions(user.id);
  } catch (e) {
    return [];
  }
});

/// Active workout session provider
final activeWorkoutSessionProvider = StateNotifierProvider<ActiveWorkoutNotifier, WorkoutSessionEnhanced?>((ref) {
  return ActiveWorkoutNotifier();
});

class ActiveWorkoutNotifier extends StateNotifier<WorkoutSessionEnhanced?> {
  ActiveWorkoutNotifier() : super(null);

  /// Start a new workout session
  void startWorkout(EnhancedWorkout workout, String userId) {
    final session = WorkoutSessionEnhanced(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      workoutId: workout.id,
      startedAt: DateTime.now(),
      workout: workout,
      exercises: workout.exercises.map((we) => SessionExercise(
        id: '${we.id}_session',
        sessionId: DateTime.now().millisecondsSinceEpoch.toString(),
        exerciseId: we.exerciseId,
        exercise: we.exercise,
        createdAt: DateTime.now(),
      )).toList(),
      createdAt: DateTime.now(),
    );
    
    state = session;
  }

  /// Update exercise progress
  void updateExerciseProgress(String exerciseId, {
    int? setsCompleted,
    int? repsCompleted,
    double? weightUsed,
    int? durationSeconds,
    bool? isCompleted,
  }) {
    if (state == null) return;

    final updatedExercises = state!.exercises.map((exercise) {
      if (exercise.exerciseId == exerciseId) {
        return exercise.copyWith(
          setsCompleted: setsCompleted ?? exercise.setsCompleted,
          repsCompleted: repsCompleted ?? exercise.repsCompleted,
          weightUsedKg: weightUsed ?? exercise.weightUsedKg,
          durationSeconds: durationSeconds ?? exercise.durationSeconds,
          isCompleted: isCompleted ?? exercise.isCompleted,
        );
      }
      return exercise;
    }).toList();

    state = state!.copyWith(exercises: updatedExercises);
  }

  /// Complete the workout session
  void completeWorkout({int? rating, String? notes, int? caloriesBurned}) {
    if (state == null) return;

    final completedAt = DateTime.now();
    final duration = completedAt.difference(state!.startedAt).inMinutes;

    state = state!.copyWith(
      completedAt: completedAt,
      durationMinutes: duration,
      rating: rating,
      notes: notes,
      caloriesBurned: caloriesBurned,
      isCompleted: true,
    );
  }

  /// Cancel the current workout
  void cancelWorkout() {
    state = null;
  }

  /// Pause the workout (for future implementation)
  void pauseWorkout() {
    // Implementation for pausing workout
  }

  /// Resume the workout (for future implementation)
  void resumeWorkout() {
    // Implementation for resuming workout
  }
}

/// Workout statistics provider
final workoutStatsProvider = FutureProvider<WorkoutStats>((ref) async {
  final sessions = await ref.watch(workoutSessionsProvider.future);
  
  return WorkoutStats.fromSessions(sessions);
});

class WorkoutStats {
  final int totalWorkouts;
  final int totalMinutes;
  final int totalCalories;
  final int currentStreak;
  final int longestStreak;
  final double averageRating;
  final Map<String, int> workoutsByCategory;
  final List<WorkoutSessionEnhanced> recentSessions;

  WorkoutStats({
    required this.totalWorkouts,
    required this.totalMinutes,
    required this.totalCalories,
    required this.currentStreak,
    required this.longestStreak,
    required this.averageRating,
    required this.workoutsByCategory,
    required this.recentSessions,
  });

  factory WorkoutStats.fromSessions(List<WorkoutSessionEnhanced> sessions) {
    final completedSessions = sessions.where((s) => s.isCompleted).toList();
    
    return WorkoutStats(
      totalWorkouts: completedSessions.length,
      totalMinutes: completedSessions.fold(0, (sum, s) => sum + (s.durationMinutes ?? 0)),
      totalCalories: completedSessions.fold(0, (sum, s) => sum + (s.caloriesBurned ?? 0)),
      currentStreak: _calculateCurrentStreak(completedSessions),
      longestStreak: _calculateLongestStreak(completedSessions),
      averageRating: _calculateAverageRating(completedSessions),
      workoutsByCategory: _groupByCategory(completedSessions),
      recentSessions: completedSessions.take(5).toList(),
    );
  }

  static int _calculateCurrentStreak(List<WorkoutSessionEnhanced> sessions) {
    // Simple implementation - count consecutive days with workouts
    // This would be more sophisticated in a real app
    return sessions.length > 0 ? 3 : 0; // Mock value
  }

  static int _calculateLongestStreak(List<WorkoutSessionEnhanced> sessions) {
    // Mock implementation
    return sessions.length > 0 ? 7 : 0; // Mock value
  }

  static double _calculateAverageRating(List<WorkoutSessionEnhanced> sessions) {
    final ratingsessions = sessions.where((s) => s.rating != null);
    if (ratingsessions.isEmpty) return 0.0;
    
    final totalRating = ratingsessions.fold(0, (sum, s) => sum + s.rating!);
    return totalRating / ratingsessions.length;
  }

  static Map<String, int> _groupByCategory(List<WorkoutSessionEnhanced> sessions) {
    final categoryCount = <String, int>{};
    
    for (final session in sessions) {
      final category = session.workout?.category ?? 'Unknown';
      categoryCount[category] = (categoryCount[category] ?? 0) + 1;
    }
    
    return categoryCount;
  }
}

// Sample data functions (to be replaced with real Supabase queries)

List<EnhancedWorkout> _getSampleWorkouts() {
  final now = DateTime.now();
  
  return [
    EnhancedWorkout(
      id: '1',
      name: 'Full Body Beginner',
      description: 'Perfect starter workout for beginners',
      category: 'full_body',
      difficulty: 'beginner',
      estimatedDuration: 20,
      equipmentNeeded: [],
      targetMuscles: ['chest', 'legs', 'core'],
      exercises: [
        WorkoutExercise(
          id: 'we1',
          workoutId: '1',
          exerciseId: 'ex1',
          orderIndex: 1,
          sets: 3,
          reps: 10,
          restSeconds: 60,
        ),
      ],
      createdAt: now,
      updatedAt: now,
    ),
    EnhancedWorkout(
      id: '2',
      name: 'HIIT Cardio Blast',
      description: 'High intensity interval training',
      category: 'cardio',
      difficulty: 'intermediate',
      estimatedDuration: 15,
      equipmentNeeded: [],
      targetMuscles: ['full_body'],
      exercises: [],
      createdAt: now,
      updatedAt: now,
    ),
  ];
}

List<EnhancedExercise> _getSampleExercises() {
  final now = DateTime.now();
  
  return [
    EnhancedExercise(
      id: 'ex1',
      name: 'Push-ups',
      description: 'Classic bodyweight chest exercise',
      instructions: [
        'Start in plank position',
        'Lower body until chest nearly touches floor',
        'Push back up to starting position'
      ],
      category: 'strength',
      equipment: 'none',
      primaryMuscle: 'chest',
      secondaryMuscles: ['triceps', 'shoulders'],
      difficulty: 'beginner',
      repsRange: '8-15',
      createdAt: now,
      updatedAt: now,
    ),
  ];
}

List<WorkoutSessionEnhanced> _getSampleSessions(String userId) {
  final now = DateTime.now();
  
  return [
    WorkoutSessionEnhanced(
      id: 'session1',
      userId: userId,
      workoutId: '1',
      startedAt: now.subtract(const Duration(days: 1)),
      completedAt: now.subtract(const Duration(days: 1, hours: -1)),
      durationMinutes: 25,
      caloriesBurned: 150,
      rating: 4,
      isCompleted: true,
      createdAt: now.subtract(const Duration(days: 1)),
    ),
  ];
}