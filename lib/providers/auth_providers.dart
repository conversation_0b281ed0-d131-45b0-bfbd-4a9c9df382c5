import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfit/services/supabase_service_simple.dart';

/// Authentication providers for the app

/// Supabase service provider
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

/// Authentication state stream provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  return Supabase.instance.client.auth.onAuthStateChange;
});

/// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.session?.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

/// User profile provider
final userProfileProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return null;

  final service = ref.read(supabaseServiceProvider);
  try {
    final result = await service.getUserProfile(user.id);
    return result;
  } catch (e) {
    // Return null on error to match the nullable return type
    return null;
  }
});

/// Authentication notifier for sign in/up/out operations
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AsyncValue<User?>>((ref) {
  return AuthNotifier(ref.read(supabaseServiceProvider));
});

class AuthNotifier extends StateNotifier<AsyncValue<User?>> {
  AuthNotifier(this._service) : super(const AsyncValue.loading()) {
    // Initialize with current user state
    _initializeAuth();
  }

  final SupabaseService _service;

  void _initializeAuth() {
    final currentUser = _service.currentUser;
    state = AsyncValue.data(currentUser);
  }

  /// Sign in with email and password
  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.signInWithEmail(
        email: email,
        password: password,
      );
      state = AsyncValue.data(response.user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.signUpWithEmail(
        email: email,
        password: password,
        displayName: displayName,
      );
      state = AsyncValue.data(response.user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    state = const AsyncValue.loading();
    try {
      await _service.signOut();
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _service.resetPassword(email);
    } catch (error) {
      // Don't change auth state for password reset
      rethrow;
    }
  }
}

/// Profile management notifier
final profileNotifierProvider = StateNotifierProvider<ProfileNotifier, AsyncValue<Map<String, dynamic>?>>((ref) {
  return ProfileNotifier(ref.read(supabaseServiceProvider));
});

class ProfileNotifier extends StateNotifier<AsyncValue<Map<String, dynamic>?>> {
  ProfileNotifier(this._service) : super(const AsyncValue.loading());

  final SupabaseService _service;

  /// Load user profile
  Future<void> loadProfile() async {
    state = const AsyncValue.loading();
    try {
      final profile = await _service.getUserProfile();
      state = AsyncValue.data(profile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update user profile
  Future<void> updateProfile(Map<String, dynamic> profileData) async {
    state = const AsyncValue.loading();
    try {
      final updatedProfile = await _service.upsertUserProfile(profileData);
      state = AsyncValue.data(updatedProfile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Check if user has completed onboarding
  bool get hasCompletedOnboarding {
    return state.when(
      data: (profile) => profile?['onboarding_completed'] == true,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Mark onboarding as completed
  Future<void> completeOnboarding() async {
    final currentProfile = state.value ?? <String, dynamic>{};
    await updateProfile({
      ...currentProfile,
      'onboarding_completed': true,
      'has_completed_preferences': true,
    });
  }
}