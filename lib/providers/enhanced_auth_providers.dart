import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfit/models/user_profile.dart';
import 'package:openfit/services/supabase_service_simple.dart';

/// Enhanced authentication providers with full Supabase integration

/// Supabase service provider
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

/// Authentication state stream provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  return Supabase.instance.client.auth.onAuthStateChange;
});

/// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.session?.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Enhanced user profile provider
final userProfileProvider = FutureProvider<UserProfile?>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return null;

  final service = ref.read(supabaseServiceProvider);
  try {
    final profileData = await service.getUserProfile(user.id);
    if (profileData == null) return null;
    return UserProfile.fromJson(profileData);
  } catch (e) {
    // Return null on error to match the nullable return type
    return null;
  }
});

/// Authentication notifier for sign in/up/out operations
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AsyncValue<User?>>((ref) {
  return AuthNotifier(ref.read(supabaseServiceProvider));
});

class AuthNotifier extends StateNotifier<AsyncValue<User?>> {
  AuthNotifier(this._service) : super(const AsyncValue.loading()) {
    // Initialize with current user state
    _initializeAuth();
  }

  final SupabaseService _service;

  void _initializeAuth() {
    final currentUser = _service.currentUser;
    state = AsyncValue.data(currentUser);
  }

  /// Sign in with email and password
  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.signInWithEmail(
        email: email,
        password: password,
      );
      state = AsyncValue.data(response.user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.signUpWithEmail(
        email: email,
        password: password,
        displayName: displayName,
      );
      state = AsyncValue.data(response.user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _service.signOut();
      // Don't set loading state - let the auth stream handle the state change
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _service.resetPassword(email);
    } catch (error) {
      // Don't change auth state for password reset
      rethrow;
    }
  }
}

/// Enhanced profile management notifier
final profileNotifierProvider = StateNotifierProvider<ProfileNotifier, AsyncValue<UserProfile?>>((ref) {
  return ProfileNotifier(ref.read(supabaseServiceProvider));
});

class ProfileNotifier extends StateNotifier<AsyncValue<UserProfile?>> {
  ProfileNotifier(this._service) : super(const AsyncValue.loading());

  final SupabaseService _service;

  /// Load user profile
  Future<void> loadProfile([String? userId]) async {
    state = const AsyncValue.loading();
    try {
      final profileData = await _service.getUserProfile(userId);
      if (profileData != null) {
        final profile = UserProfile.fromJson(profileData);
        state = AsyncValue.data(profile);
      } else {
        state = const AsyncValue.data(null);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update user profile
  Future<void> updateProfile(UserProfile profile) async {
    state = const AsyncValue.loading();
    try {
      final updatedProfileData = await _service.upsertUserProfile(profile.toJson());
      final updatedProfile = UserProfile.fromJson(updatedProfileData);
      state = AsyncValue.data(updatedProfile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update specific profile fields
  Future<void> updateProfileFields(Map<String, dynamic> updates) async {
    final currentProfile = state.value;
    if (currentProfile == null) return;

    final updatedProfile = UserProfile.fromJson({
      ...currentProfile.toJson(),
      ...updates,
      'updated_at': DateTime.now().toIso8601String(),
    });

    await updateProfile(updatedProfile);
  }

  /// Complete onboarding
  Future<void> completeOnboarding() async {
    await updateProfileFields({
      'onboarding_completed': true,
      'has_completed_preferences': true,
    });
  }

  /// Reset onboarding for testing (development only)
  Future<void> resetOnboardingForTesting() async {
    final currentProfile = state.value;
    if (currentProfile == null) {
      throw Exception('No profile found to reset');
    }
    
    state = const AsyncValue.loading();
    try {
      // Create updated profile with reset onboarding flags
      final updatedProfile = currentProfile.copyWith(
        onboardingCompleted: false,
        hasCompletedPreferences: false,
        updatedAt: DateTime.now(),
      );
      
      // Update the profile in the database
      await updateProfile(updatedProfile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  /// Check if user has completed onboarding
  bool get hasCompletedOnboarding {
    return state.when(
      data: (profile) => profile?.onboardingCompleted ?? false,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Get current profile or null
  UserProfile? get currentProfile {
    return state.when(
      data: (profile) => profile,
      loading: () => null,
      error: (_, __) => null,
    );
  }
}