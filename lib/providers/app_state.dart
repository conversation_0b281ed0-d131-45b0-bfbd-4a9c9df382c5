import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/models/user.dart';
import 'package:openfit/models/workout_session.dart';

/// App-wide state management

// User state
final userProvider = StateNotifierProvider<UserNotifier, AsyncValue<User?>>((ref) {
  return UserNotifier();
});

class UserNotifier extends StateNotifier<AsyncValue<User?>> {
  UserNotifier() : super(const AsyncValue.loading()) {
    _loadUser();
  }

  Future<void> _loadUser() async {
    try {
      // TODO: Load user from storage/API
      // For now, using sample data
      await Future.delayed(const Duration(milliseconds: 500));
      state = const AsyncValue.data(null); // No user initially
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateUser(User user) async {
    state = const AsyncValue.loading();
    try {
      // TODO: Save user to storage/API
      await Future.delayed(const Duration(milliseconds: 300));
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> signOut() async {
    state = const AsyncValue.loading();
    try {
      // TODO: Clear user data
      await Future.delayed(const Duration(milliseconds: 300));
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Current workout session state
final currentWorkoutProvider = StateNotifierProvider<CurrentWorkoutNotifier, WorkoutSession?>((ref) {
  return CurrentWorkoutNotifier();
});

class CurrentWorkoutNotifier extends StateNotifier<WorkoutSession?> {
  CurrentWorkoutNotifier() : super(null);

  void startWorkout(WorkoutSession session) {
    state = session.copyWith(
      status: WorkoutSessionStatus.inProgress,
      startTime: DateTime.now(),
    );
  }

  void pauseWorkout() {
    if (state != null) {
      state = state!.copyWith(status: WorkoutSessionStatus.paused);
    }
  }

  void resumeWorkout() {
    if (state != null) {
      state = state!.copyWith(status: WorkoutSessionStatus.inProgress);
    }
  }

  void completeWorkout({double? rating}) {
    if (state != null) {
      state = state!.copyWith(
        status: WorkoutSessionStatus.completed,
        endTime: DateTime.now(),
        completionPercentage: 100.0,
        rating: rating ?? state!.rating,
      );
    }
  }

  void cancelWorkout() {
    if (state != null) {
      state = state!.copyWith(status: WorkoutSessionStatus.cancelled);
    }
  }

  void clearWorkout() {
    state = null;
  }

  void updateProgress(double percentage) {
    if (state != null) {
      state = state!.copyWith(completionPercentage: percentage);
    }
  }
}

// App settings state
final appSettingsProvider = StateNotifierProvider<AppSettingsNotifier, AppSettings>((ref) {
  return AppSettingsNotifier();
});

class AppSettings {
  const AppSettings({
    this.isDarkMode = true,
    this.soundEnabled = true,
    this.notificationsEnabled = true,
    this.autoStartRest = true,
    this.keepScreenOn = true,
  });

  final bool isDarkMode;
  final bool soundEnabled;
  final bool notificationsEnabled;
  final bool autoStartRest;
  final bool keepScreenOn;

  AppSettings copyWith({
    bool? isDarkMode,
    bool? soundEnabled,
    bool? notificationsEnabled,
    bool? autoStartRest,
    bool? keepScreenOn,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoStartRest: autoStartRest ?? this.autoStartRest,
      keepScreenOn: keepScreenOn ?? this.keepScreenOn,
    );
  }
}

class AppSettingsNotifier extends StateNotifier<AppSettings> {
  AppSettingsNotifier() : super(const AppSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      // TODO: Load settings from SharedPreferences
      await Future.delayed(const Duration(milliseconds: 200));
      // For now, keep default settings
    } catch (error) {
      // Keep default settings on error
    }
  }

  Future<void> updateDarkMode(bool isDarkMode) async {
    state = state.copyWith(isDarkMode: isDarkMode);
    await _saveSettings();
  }

  Future<void> updateSoundEnabled(bool enabled) async {
    state = state.copyWith(soundEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateNotificationsEnabled(bool enabled) async {
    state = state.copyWith(notificationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoStartRest(bool enabled) async {
    state = state.copyWith(autoStartRest: enabled);
    await _saveSettings();
  }

  Future<void> updateKeepScreenOn(bool enabled) async {
    state = state.copyWith(keepScreenOn: enabled);
    await _saveSettings();
  }

  Future<void> _saveSettings() async {
    try {
      // TODO: Save settings to SharedPreferences
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (error) {
      // Handle save error
    }
  }
}

// Navigation state
final navigationProvider = StateNotifierProvider<NavigationNotifier, int>((ref) {
  return NavigationNotifier();
});

class NavigationNotifier extends StateNotifier<int> {
  NavigationNotifier() : super(0);

  void setIndex(int index) {
    state = index;
  }
}