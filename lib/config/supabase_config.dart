/// Supabase configuration for OpenFit
class SupabaseConfig {
  // TODO: Replace with your actual Supabase project credentials
  // Get these from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
  
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
  
  // Database table names
  static const String profilesTable = 'profiles';
  static const String workoutsTable = 'workouts';
  static const String exercisesTable = 'exercises';
  static const String workoutSessionsTable = 'workout_sessions';
  static const String userGoalsTable = 'user_goals';
  static const String progressTable = 'progress';
  
  // Storage buckets
  static const String avatarsBucket = 'avatars';
  static const String workoutImagesBucket = 'workout-images';
  
  // Auth settings
  static const bool enableEmailConfirmation = true;
  static const bool enablePasswordRecovery = true;
  
  // Development mode - set to false in production
  static const bool isDevelopment = true;
  
  // Your Supabase project credentials (SciWell Mobile)
  static const String devSupabaseUrl = 'https://xtazgqpcaujwwaswzeoh.supabase.co';
  static const String devSupabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh0YXpncXBjYXVqd3dhc3d6ZW9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE4MTA5MDUsImV4cCI6MjA0NzM4NjkwNX0.nFutcV81_Na8L-wwxFRpYg7RhqmjMrYspP2LyKbE_q0';
  
  /// Get the appropriate Supabase URL based on environment
  static String get url => isDevelopment ? devSupabaseUrl : supabaseUrl;
  
  /// Get the appropriate Supabase anon key based on environment
  static String get anonKey => isDevelopment ? devSupabaseAnonKey : supabaseAnonKey;
  
  /// Instructions for setting up real Supabase
  static const String setupInstructions = '''
🚀 To set up real Supabase authentication:

1. Go to https://supabase.com and create a new project
2. In your project dashboard, go to Settings > API
3. Copy your Project URL and anon/public key
4. Update lib/config/supabase_config.dart:
   - Replace supabaseUrl with your Project URL
   - Replace supabaseAnonKey with your anon key
   - Set isDevelopment = false
   - Set useMockAuth = false
5. Run the SQL schema in supabase_schema.sql in your SQL Editor
6. Restart your app

For now, the app runs in demo mode with mock authentication.
  ''';
}