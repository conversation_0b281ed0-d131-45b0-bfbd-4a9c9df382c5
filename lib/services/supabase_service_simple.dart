import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:openfit/core/error_handler.dart' as app_errors;
import 'package:openfit/models/supabase_exercise.dart';
import 'package:openfit/config/supabase_config.dart';

/// Enhanced Supabase service for database operations
class SupabaseService {
  static SupabaseClient get _client => Supabase.instance.client;
  
  // Singleton pattern
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  /// Initialize Supabase with configuration
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.url,
      anonKey: SupabaseConfig.anonKey,
      debug: SupabaseConfig.isDevelopment,
    );
  }

  /// Initialize with custom credentials (for testing or different environments)
  static Future<void> initializeWithCredentials({
    required String url,
    required String anonKey,
  }) async {
    await Supabase.initialize(
      url: url,
      anon<PERSON>ey: anon<PERSON><PERSON>,
      debug: SupabaseConfig.isDevelopment,
    );
  }

  /// Get current user
  User? get currentUser => _client.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // MARK: - Authentication

  /// Sign in with email and password
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to sign in: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.signInWithEmail',
      );
      rethrow;
    }
  }

  /// Sign up with email and password
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: displayName != null ? {'display_name': displayName} : null,
      );
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to sign up: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.signUpWithEmail',
      );
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to sign out: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.signOut',
      );
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to reset password: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.resetPassword',
      );
      rethrow;
    }
  }

  // MARK: - User Profile

  /// Get user profile from profiles table
  Future<Map<String, dynamic>?> getUserProfile([String? userId]) async {
    try {
      final id = userId ?? currentUser?.id;
      if (id == null) throw app_errors.ValidationException('User not authenticated');

      final response = await _client
          .from('profiles')
          .select()
          .eq('id', id)
          .maybeSingle();
      
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch user profile: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getUserProfile',
      );
      rethrow;
    }
  }

  /// Create or update user profile
  Future<Map<String, dynamic>> upsertUserProfile(Map<String, dynamic> profile) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw app_errors.ValidationException('User not authenticated');

      final profileData = {
        'id': userId,
        'email': currentUser?.email,
        'updated_at': DateTime.now().toIso8601String(),
        ...profile,
      };

      final response = await _client
          .from('profiles')
          .upsert(profileData)
          .select()
          .single();
      
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.StorageException('Failed to save user profile: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.upsertUserProfile',
      );
      rethrow;
    }
  }

  // MARK: - Workouts

  /// Get workouts for the current user
  Future<List<Map<String, dynamic>>> getUserWorkouts() async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw app_errors.ValidationException('User not authenticated');

      final response = await _client
          .from('workouts')
          .select('''
            *,
            workout_exercises (
              *,
              exercises (*)
            )
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch workouts: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getUserWorkouts',
      );
      rethrow;
    }
  }

  /// Get workout by ID
  Future<Map<String, dynamic>?> getWorkoutById(String workoutId) async {
    try {
      final response = await _client
          .from('workouts')
          .select('''
            *,
            workout_exercises (
              *,
              exercises (*)
            )
          ''')
          .eq('id', workoutId)
          .maybeSingle();
      
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch workout: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getWorkoutById',
      );
      rethrow;
    }
  }

  /// Get completed workouts for the current user
  Future<List<Map<String, dynamic>>> getCompletedWorkouts() async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw app_errors.ValidationException('User not authenticated');

      final response = await _client
          .from('completed_workouts')
          .select('''
            *,
            workouts (
              *,
              workout_exercises (
                *,
                exercises (*)
              )
            )
          ''')
          .eq('user_id', userId)
          .order('date_completed', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch completed workouts: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getCompletedWorkouts',
      );
      rethrow;
    }
  }

  /// Create a new workout
  Future<Map<String, dynamic>> createWorkout({
    required String name,
    String? notes,
    String? aiDescription,
  }) async {
    try {
      final userId = currentUser?.id;
      if (userId == null) throw app_errors.ValidationException('User not authenticated');

      final response = await _client
          .from('workouts')
          .insert({
            'user_id': userId,
            'name': name,
            'notes': notes,
            'ai_description': aiDescription,
            'is_active': true,
            'is_completed': false,
          })
          .select()
          .single();
      
      return response;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.StorageException('Failed to create workout: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.createWorkout',
      );
      rethrow;
    }
  }

  // MARK: - Exercises

  /// Get all exercises
  Future<List<SupabaseExercise>> getExercises({
    int? limit,
    String? category,
    String? equipment,
    String? primaryMuscle,
  }) async {
    try {
      var query = _client.from('exercises').select();
      
      if (category != null) {
        query = query.eq('category', category);
      }
      if (equipment != null) {
        query = query.eq('equipment', equipment);
      }
      if (primaryMuscle != null) {
        query = query.eq('primary_muscle', primaryMuscle);
      }
      
      var orderedQuery = query.order('name');
      
      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery as List;
      
      return response
          .map((json) => SupabaseExercise.fromJson(json))
          .toList();
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch exercises: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getExercises',
      );
      rethrow;
    }
  }

  /// Search exercises by name
  Future<List<SupabaseExercise>> searchExercises(String query) async {
    try {
      final response = await _client
          .from('exercises')
          .select()
          .ilike('name', '%$query%')
          .limit(20);
      
      return (response as List)
          .map((json) => SupabaseExercise.fromJson(json))
          .toList();
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to search exercises: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.searchExercises',
      );
      rethrow;
    }
  }

  /// Get exercise by ID
  Future<SupabaseExercise?> getExerciseById(String id) async {
    try {
      final response = await _client
          .from('exercises')
          .select()
          .eq('id', id)
          .maybeSingle();
      
      return response != null ? SupabaseExercise.fromJson(response) : null;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch exercise: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getExerciseById',
      );
      rethrow;
    }
  }

  /// Get unique values for filtering
  Future<List<String>> getUniqueValues(String table, String column) async {
    try {
      final response = await _client
          .from(table)
          .select(column)
          .not(column, 'is', null);
      
      final values = (response as List)
          .map((item) => item[column] as String?)
          .where((value) => value != null && value.isNotEmpty)
          .cast<String>()
          .toSet()
          .toList();
      
      values.sort();
      return values;
    } catch (error, stackTrace) {
      app_errors.ErrorHandler.handleError(
        app_errors.NetworkException('Failed to fetch unique values: ${error.toString()}'),
        stackTrace,
        context: 'SupabaseService.getUniqueValues',
      );
      rethrow;
    }
  }

  /// Get exercise categories
  Future<List<String>> getExerciseCategories() async {
    return getUniqueValues('exercises', 'category');
  }

  /// Get equipment types
  Future<List<String>> getEquipmentTypes() async {
    return getUniqueValues('exercises', 'equipment');
  }

  /// Get primary muscles
  Future<List<String>> getPrimaryMuscles() async {
    return getUniqueValues('exercises', 'primary_muscle');
  }
}