import 'package:flutter/material.dart';
import 'dart:math' as math;

class CelebrationAnimation extends StatefulWidget {
  final bool isVisible;
  final VoidCallback? onAnimationComplete;
  final Duration duration;
  final String message;
  final String subtitle;

  const CelebrationAnimation({
    super.key,
    required this.isVisible,
    this.onAnimationComplete,
    this.duration = const Duration(milliseconds: 3000),
    this.message = 'Congratulations!',
    this.subtitle = 'You\'ve completed your profile setup',
  });

  @override
  State<CelebrationAnimation> createState() => _CelebrationAnimationState();
}

class _CelebrationAnimationState extends State<CelebrationAnimation>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _confettiController;
  late AnimationController _textController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _confettiAnimation;
  late Animation<double> _textSlideAnimation;
  late Animation<double> _textFadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _mainController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _confettiController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.3, curve: Curves.elasticOut),
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
    ));
    
    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _confettiController,
      curve: Curves.easeOut,
    ));
    
    _textSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutBack,
    ));
    
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOut,
    ));
    
    _mainController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onAnimationComplete?.call();
      }
    });
  }

  @override
  void didUpdateWidget(CelebrationAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible && !oldWidget.isVisible) {
      _startAnimation();
    } else if (!widget.isVisible && oldWidget.isVisible) {
      _resetAnimation();
    }
  }

  void _startAnimation() async {
    _mainController.forward();
    
    // Start confetti after a short delay
    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) {
      _confettiController.repeat();
    }
    
    // Start text animation
    await Future.delayed(const Duration(milliseconds: 400));
    if (mounted) {
      _textController.forward();
    }
  }

  void _resetAnimation() {
    _mainController.reset();
    _confettiController.reset();
    _textController.reset();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _confettiController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Stack(
          children: [
            // Confetti particles
            ...List.generate(20, (index) => _buildConfettiParticle(index)),
            
            // Main celebration content
            Center(
              child: AnimatedBuilder(
                animation: _mainController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: Container(
                        margin: const EdgeInsets.all(40),
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2A2D3A),
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: const Color(0xFFFF6B47).withValues(alpha: 0.3),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFFFF6B47).withValues(alpha: 0.2),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Success icon
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: const Color(0xFFFF6B47),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFFFF6B47).withValues(alpha: 0.3),
                                    blurRadius: 15,
                                    spreadRadius: 3,
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.check,
                                size: 40,
                                color: Colors.white,
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Animated text
                            AnimatedBuilder(
                              animation: _textController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(0, _textSlideAnimation.value),
                                  child: Opacity(
                                    opacity: _textFadeAnimation.value,
                                    child: Column(
                                      children: [
                                        Text(
                                          widget.message,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        
                                        const SizedBox(height: 12),
                                        
                                        Text(
                                          widget.subtitle,
                                          style: TextStyle(
                                            color: Colors.white.withValues(alpha: 0.8),
                                            fontSize: 16,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfettiParticle(int index) {
    final random = math.Random(index);
    final startX = random.nextDouble();
    final startY = -0.1;
    final endY = 1.1;
    final colors = [
      const Color(0xFFFF6B47),
      const Color(0xFF4ADE80),
      const Color(0xFF3B82F6),
      const Color(0xFFF59E0B),
      const Color(0xFFEF4444),
      const Color(0xFF8B5CF6),
    ];
    final color = colors[random.nextInt(colors.length)];
    
    return AnimatedBuilder(
      animation: _confettiAnimation,
      builder: (context, child) {
        final progress = _confettiAnimation.value;
        final currentY = startY + (endY - startY) * progress;
        final rotation = progress * 4 * math.pi;
        final opacity = (1.0 - progress).clamp(0.0, 1.0);
        
        return Positioned(
          left: MediaQuery.of(context).size.width * startX,
          top: MediaQuery.of(context).size.height * currentY,
          child: Transform.rotate(
            angle: rotation,
            child: Opacity(
              opacity: opacity,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: random.nextBool() ? BoxShape.circle : BoxShape.rectangle,
                  borderRadius: random.nextBool() ? null : BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Celebration trigger widget
class CelebrationTrigger extends StatefulWidget {
  final Widget child;
  final bool shouldCelebrate;
  final String celebrationMessage;
  final String celebrationSubtitle;
  final VoidCallback? onCelebrationComplete;

  const CelebrationTrigger({
    super.key,
    required this.child,
    required this.shouldCelebrate,
    this.celebrationMessage = 'Congratulations!',
    this.celebrationSubtitle = 'You\'ve completed your profile setup',
    this.onCelebrationComplete,
  });

  @override
  State<CelebrationTrigger> createState() => _CelebrationTriggerState();
}

class _CelebrationTriggerState extends State<CelebrationTrigger> {
  bool _showCelebration = false;

  @override
  void didUpdateWidget(CelebrationTrigger oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.shouldCelebrate && !oldWidget.shouldCelebrate) {
      setState(() {
        _showCelebration = true;
      });
    }
  }

  void _onCelebrationComplete() {
    setState(() {
      _showCelebration = false;
    });
    widget.onCelebrationComplete?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        CelebrationAnimation(
          isVisible: _showCelebration,
          message: widget.celebrationMessage,
          subtitle: widget.celebrationSubtitle,
          onAnimationComplete: _onCelebrationComplete,
        ),
      ],
    );
  }
}