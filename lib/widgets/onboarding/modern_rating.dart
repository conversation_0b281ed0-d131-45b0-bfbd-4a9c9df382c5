import 'package:flutter/material.dart';

enum RatingType {
  stars,
  scale,
  emoji,
  thumbs,
}

class ModernRating extends StatefulWidget {
  final int value;
  final int maxRating;
  final ValueChanged<int> onRatingChanged;
  final RatingType type;
  final String? label;
  final String? description;
  final List<String>? scaleLabels;
  final List<String>? emojiList;
  final bool allowZero;
  final double size;

  const ModernRating({
    super.key,
    required this.value,
    required this.maxRating,
    required this.onRatingChanged,
    this.type = RatingType.stars,
    this.label,
    this.description,
    this.scaleLabels,
    this.emojiList,
    this.allowZero = false,
    this.size = 40,
  });

  @override
  State<ModernRating> createState() => _ModernRatingState();
}

class _ModernRatingState extends State<ModernRating>
    with TickerProviderStateMixin {
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;
  int _hoveredIndex = -1;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      widget.maxRating,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 150),
        vsync: this,
      ),
    );

    _scaleAnimations = _animationControllers.map((controller) {
      return Tween<double>(
        begin: 1.0,
        end: 1.2,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();
  }

  @override
  void dispose() {
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onRatingTap(int index) {
    final newRating = index + 1;
    
    // Allow deselecting if allowZero is true and same rating is tapped
    if (widget.allowZero && widget.value == newRating) {
      widget.onRatingChanged(0);
    } else {
      widget.onRatingChanged(newRating);
    }

    // Animate the tapped item
    _animationControllers[index].forward().then((_) {
      _animationControllers[index].reverse();
    });
  }

  void _onHover(int index) {
    setState(() {
      _hoveredIndex = index;
    });
  }

  void _onHoverExit() {
    setState(() {
      _hoveredIndex = -1;
    });
  }

  Widget _buildStarRating() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        final isSelected = index < widget.value;
        final isHovered = index <= _hoveredIndex;
        
        return AnimatedBuilder(
          animation: _scaleAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[index].value,
              child: GestureDetector(
                onTap: () => _onRatingTap(index),
                child: MouseRegion(
                  onEnter: (_) => _onHover(index),
                  onExit: (_) => _onHoverExit(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      isSelected || isHovered ? Icons.star : Icons.star_border,
                      size: widget.size,
                      color: isSelected || isHovered 
                          ? const Color(0xFFFF6B47)
                          : Colors.white.withOpacity(0.4),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildScaleRating() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        final isSelected = index < widget.value;
        final isHovered = index <= _hoveredIndex;
        
        return AnimatedBuilder(
          animation: _scaleAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[index].value,
              child: GestureDetector(
                onTap: () => _onRatingTap(index),
                child: MouseRegion(
                  onEnter: (_) => _onHover(index),
                  onExit: (_) => _onHoverExit(),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: widget.size,
                    height: widget.size,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected || isHovered 
                          ? const Color(0xFFFF6B47)
                          : Colors.white.withOpacity(0.2),
                      border: Border.all(
                        color: isSelected || isHovered 
                            ? const Color(0xFFFF6B47)
                            : Colors.white.withOpacity(0.4),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: isSelected || isHovered 
                              ? Colors.white
                              : Colors.white.withOpacity(0.6),
                          fontWeight: FontWeight.w600,
                          fontSize: widget.size * 0.4,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildEmojiRating() {
    final emojis = widget.emojiList ?? ['😢', '😕', '😐', '😊', '😍'];
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        final isSelected = index < widget.value;
        final isHovered = index <= _hoveredIndex;
        
        return AnimatedBuilder(
          animation: _scaleAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[index].value,
              child: GestureDetector(
                onTap: () => _onRatingTap(index),
                child: MouseRegion(
                  onEnter: (_) => _onHover(index),
                  onExit: (_) => _onHoverExit(),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected || isHovered 
                          ? const Color(0xFFFF6B47).withOpacity(0.2)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected || isHovered 
                            ? const Color(0xFFFF6B47)
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Text(
                      index < emojis.length ? emojis[index] : '😐',
                      style: TextStyle(
                        fontSize: widget.size * 0.8,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildThumbsRating() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Thumbs down
        AnimatedBuilder(
          animation: _scaleAnimations[0],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[0].value,
              child: GestureDetector(
                onTap: () => widget.onRatingChanged(widget.value == 1 ? 0 : 1),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.value == 1 
                        ? Colors.red.withOpacity(0.2)
                        : Colors.white.withOpacity(0.1),
                    border: Border.all(
                      color: widget.value == 1 
                          ? Colors.red
                          : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.thumb_down,
                    size: widget.size * 0.6,
                    color: widget.value == 1 
                        ? Colors.red
                        : Colors.white.withOpacity(0.6),
                  ),
                ),
              ),
            );
          },
        ),
        
        // Thumbs up
        AnimatedBuilder(
          animation: _scaleAnimations[1],
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimations[1].value,
              child: GestureDetector(
                onTap: () => widget.onRatingChanged(widget.value == 2 ? 0 : 2),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.value == 2 
                        ? const Color(0xFFFF6B47).withOpacity(0.2)
                        : Colors.white.withOpacity(0.1),
                    border: Border.all(
                      color: widget.value == 2 
                          ? const Color(0xFFFF6B47)
                          : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.thumb_up,
                    size: widget.size * 0.6,
                    color: widget.value == 2 
                        ? const Color(0xFFFF6B47)
                        : Colors.white.withOpacity(0.6),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Label
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white.withOpacity(0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // Rating widget
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: const Color(0xFF2A2D3A).withOpacity(0.6),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: Column(
            children: [
              // Rating display
              if (widget.type == RatingType.stars) _buildStarRating(),
              if (widget.type == RatingType.scale) _buildScaleRating(),
              if (widget.type == RatingType.emoji) _buildEmojiRating(),
              if (widget.type == RatingType.thumbs) _buildThumbsRating(),
              
              // Scale labels
              if (widget.scaleLabels != null && widget.type == RatingType.scale) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: widget.scaleLabels!.map((label) {
                    return Text(
                      label,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.6),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
        
        // Description
        if (widget.description != null) ...[
          const SizedBox(height: 12),
          Text(
            widget.description!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
        
        // Current value display
        if (widget.value > 0) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: const Color(0xFFFF6B47).withOpacity(0.2),
            ),
            child: Text(
              'Rating: ${widget.value}/${widget.maxRating}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: const Color(0xFFFF6B47),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }
}