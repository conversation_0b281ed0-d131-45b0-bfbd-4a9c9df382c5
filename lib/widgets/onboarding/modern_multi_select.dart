import 'package:flutter/material.dart';

class ModernMultiSelectOption<T> {
  final T value;
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? customIcon;

  const ModernMultiSelectOption({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
    this.customIcon,
  });
}

class ModernMultiSelect<T> extends StatefulWidget {
  final List<ModernMultiSelectOption<T>> options;
  final List<T> selectedValues;
  final ValueChanged<List<T>> onSelectionChanged;
  final String? title;
  final String? subtitle;
  final int? maxSelections;
  final int? minSelections;
  final bool allowEmpty;
  final CrossAxisAlignment crossAxisAlignment;
  final int columnsCount;

  const ModernMultiSelect({
    super.key,
    required this.options,
    required this.selectedValues,
    required this.onSelectionChanged,
    this.title,
    this.subtitle,
    this.maxSelections,
    this.minSelections,
    this.allowEmpty = true,
    this.crossAxisAlignment = CrossAxisAlignment.stretch,
    this.columnsCount = 1,
  });

  @override
  State<ModernMultiSelect<T>> createState() => _ModernMultiSelectState<T>();
}

class _ModernMultiSelectState<T> extends State<ModernMultiSelect<T>>
    with TickerProviderStateMixin {
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      widget.options.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    _scaleAnimations = _animationControllers.map((controller) {
      return Tween<double>(
        begin: 1.0,
        end: 0.95,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();
  }

  @override
  void dispose() {
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _toggleSelection(T value, int index) {
    final currentSelection = List<T>.from(widget.selectedValues);
    final isSelected = currentSelection.contains(value);

    if (isSelected) {
      // Check minimum selections
      if (widget.minSelections != null && 
          currentSelection.length <= widget.minSelections!) {
        _showSelectionError('Minimum ${widget.minSelections} selections required');
        return;
      }
      currentSelection.remove(value);
    } else {
      // Check maximum selections
      if (widget.maxSelections != null && 
          currentSelection.length >= widget.maxSelections!) {
        _showSelectionError('Maximum ${widget.maxSelections} selections allowed');
        return;
      }
      currentSelection.add(value);
    }

    // Animate the card
    _animationControllers[index].forward().then((_) {
      _animationControllers[index].reverse();
    });

    widget.onSelectionChanged(currentSelection);
  }

  void _showSelectionError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.withOpacity(0.8),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildOption(ModernMultiSelectOption<T> option, int index) {
    final isSelected = widget.selectedValues.contains(option.value);
    
    return AnimatedBuilder(
      animation: _scaleAnimations[index],
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimations[index].value,
          child: GestureDetector(
            onTap: () => _toggleSelection(option.value, index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: isSelected 
                    ? const Color(0xFF2A2D3A).withOpacity(0.9)
                    : const Color(0xFF2A2D3A).withOpacity(0.6),
                border: Border.all(
                  color: isSelected 
                      ? const Color(0xFFFF6B47)
                      : Colors.white.withOpacity(0.1),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: const Color(0xFFFF6B47).withOpacity(0.2),
                    blurRadius: 12,
                    spreadRadius: 1,
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Icon
                  if (option.customIcon != null) ...[
                    option.customIcon!,
                    const SizedBox(width: 12),
                  ] else if (option.icon != null) ...[
                    Icon(
                      option.icon,
                      size: 24,
                      color: isSelected 
                          ? const Color(0xFFFF6B47)
                          : Colors.white.withOpacity(0.7),
                    ),
                    const SizedBox(width: 12),
                  ],
                  
                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          option.title,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: isSelected 
                                ? Colors.white
                                : Colors.white.withOpacity(0.9),
                            fontWeight: isSelected 
                                ? FontWeight.w600
                                : FontWeight.w500,
                          ),
                        ),
                        if (option.subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            option.subtitle!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Selection indicator
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected 
                          ? const Color(0xFFFF6B47)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFFFF6B47)
                            : Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: isSelected 
                        ? const Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: widget.crossAxisAlignment,
      children: [
        // Header
        if (widget.title != null) ...[
          Text(
            widget.title!,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        if (widget.subtitle != null) ...[
          Text(
            widget.subtitle!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
        ],
        
        // Selection counter
        if (widget.maxSelections != null || widget.minSelections != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xFFFF6B47).withOpacity(0.1),
              border: Border.all(
                color: const Color(0xFFFF6B47).withOpacity(0.3),
              ),
            ),
            child: Text(
              '${widget.selectedValues.length}/${widget.maxSelections ?? '∞'} selected',
              style: theme.textTheme.bodySmall?.copyWith(
                color: const Color(0xFFFF6B47),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // Options
        if (widget.columnsCount == 1) ...[
          // Single column layout
          ...widget.options.asMap().entries.map((entry) {
            return _buildOption(entry.value, entry.key);
          }),
        ] else ...[
          // Multi-column layout
          ...List.generate(
            (widget.options.length / widget.columnsCount).ceil(),
            (rowIndex) {
              final startIndex = rowIndex * widget.columnsCount;
              final endIndex = (startIndex + widget.columnsCount)
                  .clamp(0, widget.options.length);
              
              return Row(
                children: List.generate(
                  widget.columnsCount,
                  (colIndex) {
                    final optionIndex = startIndex + colIndex;
                    if (optionIndex >= endIndex) {
                      return Expanded(child: Container());
                    }
                    
                    return Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(
                          right: colIndex < widget.columnsCount - 1 ? 8 : 0,
                        ),
                        child: _buildOption(
                          widget.options[optionIndex],
                          optionIndex,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ],
    );
  }
}