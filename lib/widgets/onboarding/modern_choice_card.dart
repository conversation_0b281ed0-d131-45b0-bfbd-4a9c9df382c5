import 'package:flutter/material.dart';

class ModernChoiceCard extends StatefulWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool isSelected;
  final VoidCallback onTap;
  final bool enabled;
  final Widget? customIcon;

  const ModernChoiceCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.isSelected,
    required this.onTap,
    this.enabled = true,
    this.customIcon,
  });

  @override
  State<ModernChoiceCard> createState() => _ModernChoiceCardState();
}

class _ModernChoiceCardState extends State<ModernChoiceCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.enabled ? (_) => _animationController.forward() : null,
            onTapUp: widget.enabled ? (_) => _animationController.reverse() : null,
            onTapCancel: widget.enabled ? () => _animationController.reverse() : null,
            onTap: widget.enabled ? widget.onTap : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: widget.isSelected 
                    ? const Color(0xFF2A2D3A).withOpacity(0.9)
                    : const Color(0xFF2A2D3A).withOpacity(0.6),
                border: Border.all(
                  color: widget.isSelected 
                      ? const Color(0xFFFF6B47)
                      : Colors.white.withOpacity(0.1),
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: [
                  if (widget.isSelected) ...[
                    BoxShadow(
                      color: const Color(0xFFFF6B47).withOpacity(0.3 * _glowAnimation.value),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.customIcon != null) ...[
                    widget.customIcon!,
                    const SizedBox(height: 12),
                  ] else if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      size: 32,
                      color: widget.isSelected 
                          ? const Color(0xFFFF6B47)
                          : Colors.white.withOpacity(0.7),
                    ),
                    const SizedBox(height: 12),
                  ],
                  Text(
                    widget.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: widget.isSelected 
                          ? Colors.white
                          : Colors.white.withOpacity(0.9),
                      fontWeight: widget.isSelected 
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      widget.subtitle!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  if (widget.isSelected) ...[
                    const SizedBox(height: 12),
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: const Color(0xFFFF6B47),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFFF6B47).withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}