import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ModernTextInput extends StatefulWidget {
  final String label;
  final String? hint;
  final String? initialValue;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final int? maxLength;
  final int maxLines;
  final bool readOnly;
  final bool enabled;
  final TextCapitalization textCapitalization;

  const ModernTextInput({
    super.key,
    required this.label,
    this.hint,
    this.initialValue,
    this.onChanged,
    this.onTap,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.obscureText = false,
    this.suffixIcon,
    this.prefixIcon,
    this.maxLength,
    this.maxLines = 1,
    this.readOnly = false,
    this.enabled = true,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  State<ModernTextInput> createState() => _ModernTextInputState();
}

class _ModernTextInputState extends State<ModernTextInput>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _labelAnimation;
  late Animation<Color?> _borderColorAnimation;
  
  bool _hasError = false;
  String? _errorText;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _labelAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _borderColorAnimation = ColorTween(
      begin: Colors.white.withOpacity(0.2),
      end: const Color(0xFFFF6B47),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _focusNode.addListener(_onFocusChange);
    _controller.addListener(_onTextChange);
    
    // Initialize animation state
    if (_controller.text.isNotEmpty) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (_focusNode.hasFocus || _controller.text.isNotEmpty) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _onTextChange() {
    if (widget.onChanged != null) {
      widget.onChanged!(_controller.text);
    }
    
    // Validate on text change
    if (widget.validator != null) {
      final error = widget.validator!(_controller.text);
      setState(() {
        _hasError = error != null;
        _errorText = error;
      });
    }
    
    // Handle label animation
    if (_controller.text.isNotEmpty && _animationController.value == 0) {
      _animationController.forward();
    } else if (_controller.text.isEmpty && !_isFocused && _animationController.value == 1) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF2A2D3A).withOpacity(0.6),
                border: Border.all(
                  color: _hasError 
                      ? Colors.red.withOpacity(0.7)
                      : _borderColorAnimation.value ?? Colors.white.withOpacity(0.2),
                  width: _isFocused ? 2 : 1,
                ),
                boxShadow: _isFocused ? [
                  BoxShadow(
                    color: (_hasError ? Colors.red : const Color(0xFFFF6B47)).withOpacity(0.2),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ] : null,
              ),
              child: Stack(
                children: [
                  // Text field
                  Padding(
                    padding: EdgeInsets.only(
                      left: widget.prefixIcon != null ? 48 : 16,
                      right: widget.suffixIcon != null ? 48 : 16,
                      top: 20,
                      bottom: 16,
                    ),
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      onTap: widget.onTap,
                      keyboardType: widget.keyboardType,
                      inputFormatters: widget.inputFormatters,
                      obscureText: widget.obscureText,
                      maxLength: widget.maxLength,
                      maxLines: widget.maxLines,
                      readOnly: widget.readOnly,
                      enabled: widget.enabled,
                      textCapitalization: widget.textCapitalization,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: Colors.white,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: _animationController.value < 0.5 ? widget.hint : null,
                        hintStyle: TextStyle(
                          color: Colors.white.withOpacity(0.5),
                        ),
                        counterText: '',
                      ),
                    ),
                  ),
                  
                  // Floating label
                  Positioned(
                    left: widget.prefixIcon != null ? 48 : 16,
                    top: Tween<double>(
                      begin: 20,
                      end: 8,
                    ).evaluate(_labelAnimation),
                    child: Transform.scale(
                      scale: Tween<double>(
                        begin: 1.0,
                        end: 0.8,
                      ).evaluate(_labelAnimation),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        widget.label,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _hasError 
                              ? Colors.red.withOpacity(0.7)
                              : _isFocused 
                                  ? const Color(0xFFFF6B47)
                                  : Colors.white.withOpacity(0.6),
                          fontWeight: _isFocused ? FontWeight.w500 : FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  
                  // Prefix icon
                  if (widget.prefixIcon != null)
                    Positioned(
                      left: 16,
                      top: 0,
                      bottom: 0,
                      child: Center(child: widget.prefixIcon!),
                    ),
                  
                  // Suffix icon
                  if (widget.suffixIcon != null)
                    Positioned(
                      right: 16,
                      top: 0,
                      bottom: 0,
                      child: Center(child: widget.suffixIcon!),
                    ),
                ],
              ),
            );
          },
        ),
        
        // Error text
        if (_hasError && _errorText != null) ...[
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Text(
              _errorText!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.red.withOpacity(0.7),
              ),
            ),
          ),
        ],
        
        // Character count
        if (widget.maxLength != null) ...[
          const SizedBox(height: 4),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '${_controller.text.length}/${widget.maxLength}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ],
      ],
    );
  }
}