import 'package:flutter/material.dart';
import 'dart:math' as math;

class OnboardingProgress extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepTitles;
  final bool showPercentage;
  final bool showStepNumbers;
  final Color? primaryColor;
  final Color? backgroundColor;
  final double height;
  final EdgeInsets padding;
  final bool animated;
  final Duration animationDuration;

  const OnboardingProgress({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.stepTitles = const [],
    this.showPercentage = true,
    this.showStepNumbers = false,
    this.primaryColor,
    this.backgroundColor,
    this.height = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 20),
    this.animated = true,
    this.animationDuration = const Duration(milliseconds: 800),
  });

  @override
  State<OnboardingProgress> createState() => _OnboardingProgressState();
}

class _OnboardingProgressState extends State<OnboardingProgress>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;
  
  double _displayProgress = 0.0;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOutCubic,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _progressController.addListener(() {
      setState(() {
        _displayProgress = _progressAnimation.value * (widget.currentStep / widget.totalSteps);
      });
    });
    
    // Start animation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateProgress();
    });
  }

  @override
  void didUpdateWidget(OnboardingProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _updateProgress();
      
      // Add pulse effect when step changes
      if (widget.currentStep > oldWidget.currentStep) {
        _pulseController.forward().then((_) {
          _pulseController.reverse();
        });
      }
    }
  }

  void _updateProgress() {
    if (widget.animated) {
      _progressController.forward();
    } else {
      setState(() {
        _displayProgress = widget.currentStep / widget.totalSteps;
      });
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  double get progressPercentage => (widget.currentStep / widget.totalSteps * 100).clamp(0, 100);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.primaryColor ?? const Color(0xFFFF6B47);
    final backgroundColor = widget.backgroundColor ?? Colors.white.withValues(alpha: 0.1);
    
    return Padding(
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress bar with step indicators
          SizedBox(
            height: widget.height + 20, // Extra space for step indicators
            child: Stack(
              children: [
                // Background track
                Positioned(
                  top: 10,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: widget.height,
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(widget.height / 2),
                    ),
                  ),
                ),
                
                // Progress track
                Positioned(
                  top: 10,
                  left: 0,
                  right: 0,
                  child: AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Container(
                        height: widget.height,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(widget.height / 2),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(widget.height / 2),
                          child: LinearProgressIndicator(
                            value: _displayProgress,
                            backgroundColor: Colors.transparent,
                            valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                
                // Step indicators
                if (widget.showStepNumbers) ...[
                  for (int i = 0; i < widget.totalSteps; i++)
                    _buildStepIndicator(i, primaryColor, backgroundColor),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Progress info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Current step info
              if (widget.stepTitles.isNotEmpty && 
                  widget.currentStep > 0 && 
                  widget.currentStep <= widget.stepTitles.length) ...[
                Expanded(
                  child: Text(
                    widget.stepTitles[widget.currentStep - 1],
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ] else ...[
                Text(
                  'Step ${widget.currentStep} of ${widget.totalSteps}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
              
              // Percentage
              if (widget.showPercentage) ...[
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: primaryColor.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          '${progressPercentage.round()}%',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int stepIndex, Color primaryColor, Color backgroundColor) {
    final isCompleted = stepIndex < widget.currentStep;
    final isCurrent = stepIndex == widget.currentStep - 1;
    final stepPosition = stepIndex / (widget.totalSteps - 1);
    
    return Positioned(
      left: stepPosition * (MediaQuery.of(context).size.width - widget.padding.horizontal * 2 - 20),
      top: 0,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isCompleted || isCurrent ? primaryColor : backgroundColor,
          border: Border.all(
            color: isCompleted || isCurrent ? primaryColor : Colors.white.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: isCompleted || isCurrent ? [
            BoxShadow(
              color: primaryColor.withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ] : null,
        ),
        child: isCompleted
            ? const Icon(
                Icons.check,
                size: 12,
                color: Colors.white,
              )
            : isCurrent
                ? Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                  )
                : null,
      ),
    );
  }
}

// Progress persistence helper
class OnboardingProgressPersistence {
  static const String _progressKey = 'onboarding_progress';
  static const String _stepDataKey = 'onboarding_step_data';
  
  // Save progress to local storage
  static Future<void> saveProgress({
    required int currentStep,
    required int totalSteps,
    Map<String, dynamic>? stepData,
  }) async {
    // This would typically use SharedPreferences or similar
    // For now, we'll use a simple in-memory storage
    _InMemoryStorage.setInt(_progressKey, currentStep);
    if (stepData != null) {
      _InMemoryStorage.setMap(_stepDataKey, stepData);
    }
  }
  
  // Load progress from local storage
  static Future<OnboardingProgressData?> loadProgress() async {
    final currentStep = _InMemoryStorage.getInt(_progressKey);
    final stepData = _InMemoryStorage.getMap(_stepDataKey);
    
    if (currentStep != null) {
      return OnboardingProgressData(
        currentStep: currentStep,
        stepData: stepData ?? {},
      );
    }
    
    return null;
  }
  
  // Clear saved progress
  static Future<void> clearProgress() async {
    _InMemoryStorage.remove(_progressKey);
    _InMemoryStorage.remove(_stepDataKey);
  }
}

class OnboardingProgressData {
  final int currentStep;
  final Map<String, dynamic> stepData;
  
  const OnboardingProgressData({
    required this.currentStep,
    required this.stepData,
  });
}

// Simple in-memory storage for demo purposes
// In a real app, this would use SharedPreferences
class _InMemoryStorage {
  static final Map<String, dynamic> _storage = {};
  
  static void setInt(String key, int value) {
    _storage[key] = value;
  }
  
  static void setMap(String key, Map<String, dynamic> value) {
    _storage[key] = value;
  }
  
  static int? getInt(String key) {
    return _storage[key] as int?;
  }
  
  static Map<String, dynamic>? getMap(String key) {
    return _storage[key] as Map<String, dynamic>?;
  }
  
  static void remove(String key) {
    _storage.remove(key);
  }
}