import 'package:flutter/material.dart';

class ModernSlider extends StatefulWidget {
  final double value;
  final double min;
  final double max;
  final int? divisions;
  final String? label;
  final String? unit;
  final ValueChanged<double> onChanged;
  final String Function(double)? valueFormatter;
  final bool showValue;

  const ModernSlider({
    super.key,
    required this.value,
    required this.min,
    required this.max,
    this.divisions,
    this.label,
    this.unit,
    required this.onChanged,
    this.valueFormatter,
    this.showValue = true,
  });

  @override
  State<ModernSlider> createState() => _ModernSliderState();
}

class _ModernSliderState extends State<ModernSlider>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _formatValue(double value) {
    if (widget.valueFormatter != null) {
      return widget.valueFormatter!(value);
    }
    
    if (value == value.roundToDouble()) {
      return '${value.round()}${widget.unit ?? ''}';
    }
    return '${value.toStringAsFixed(1)}${widget.unit ?? ''}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white.withOpacity(0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // Value display
        if (widget.showValue) ...[
          Center(
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: const Color(0xFFFF6B47).withOpacity(0.2),
                      border: Border.all(
                        color: const Color(0xFFFF6B47).withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      _formatValue(widget.value),
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: const Color(0xFFFF6B47),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 24),
        ],
        
        // Custom slider
        SizedBox(
          height: 60,
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 6,
              activeTrackColor: const Color(0xFFFF6B47),
              inactiveTrackColor: Colors.white.withOpacity(0.1),
              thumbColor: const Color(0xFFFF6B47),
              thumbShape: CustomSliderThumb(
                isDragging: _isDragging,
              ),
              overlayColor: const Color(0xFFFF6B47).withOpacity(0.2),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              tickMarkShape: const RoundSliderTickMarkShape(tickMarkRadius: 3),
              activeTickMarkColor: const Color(0xFFFF6B47).withOpacity(0.7),
              inactiveTickMarkColor: Colors.white.withOpacity(0.3),
              valueIndicatorColor: const Color(0xFFFF6B47),
              valueIndicatorTextStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            child: Slider(
              value: widget.value,
              min: widget.min,
              max: widget.max,
              divisions: widget.divisions,
              onChanged: widget.onChanged,
              onChangeStart: (_) {
                setState(() => _isDragging = true);
                _animationController.forward();
              },
              onChangeEnd: (_) {
                setState(() => _isDragging = false);
                _animationController.reverse();
              },
            ),
          ),
        ),
        
        // Min/Max labels
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatValue(widget.min),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
              Text(
                _formatValue(widget.max),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CustomSliderThumb extends SliderComponentShape {
  final bool isDragging;
  
  const CustomSliderThumb({this.isDragging = false});

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return const Size(24, 24);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    
    // Outer glow
    if (isDragging) {
      final glowPaint = Paint()
        ..color = const Color(0xFFFF6B47).withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
      
      canvas.drawCircle(center, 16, glowPaint);
    }
    
    // Main thumb
    final thumbPaint = Paint()
      ..color = const Color(0xFFFF6B47)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, isDragging ? 14 : 12, thumbPaint);
    
    // Inner highlight
    final highlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, isDragging ? 6 : 5, highlightPaint);
  }
}