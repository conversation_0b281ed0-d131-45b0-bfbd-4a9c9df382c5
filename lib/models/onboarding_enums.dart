import 'package:flutter/material.dart';

/// Gender options for user profile
enum Gender {
  male,
  female,
  nonBinary,
  preferNotToSay;

  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.nonBinary:
        return 'Non-binary';
      case Gender.preferNotToSay:
        return 'Prefer not to say';
    }
  }

  IconData get icon {
    switch (this) {
      case Gender.male:
        return Icons.male;
      case Gender.female:
        return Icons.female;
      case Gender.nonBinary:
        return Icons.transgender;
      case Gender.preferNotToSay:
        return Icons.help_outline;
    }
  }
}

/// Height measurement units
enum HeightUnit {
  feet,
  cm;

  String get displayName {
    switch (this) {
      case HeightUnit.feet:
        return 'ft';
      case HeightUnit.cm:
        return 'cm';
    }
  }
}

/// Weight measurement units
enum WeightUnit {
  lbs,
  kg;

  String get displayName {
    switch (this) {
      case WeightUnit.lbs:
        return 'lbs';
      case WeightUnit.kg:
        return 'kg';
    }
  }
}

/// Primary fitness goals
enum FitnessGoal {
  weightLoss,
  muscleGain,
  endurance,
  strength,
  flexibility,
  generalHealth,
  stressRelief,
  bodyToning,
  athleticPerformance,
  rehabilitation;

  String get displayName {
    switch (this) {
      case FitnessGoal.weightLoss:
        return 'Weight Loss';
      case FitnessGoal.muscleGain:
        return 'Muscle Gain';
      case FitnessGoal.endurance:
        return 'Endurance';
      case FitnessGoal.strength:
        return 'Strength';
      case FitnessGoal.flexibility:
        return 'Flexibility';
      case FitnessGoal.generalHealth:
        return 'General Health';
      case FitnessGoal.stressRelief:
        return 'Stress Relief';
      case FitnessGoal.bodyToning:
        return 'Body Toning';
      case FitnessGoal.athleticPerformance:
        return 'Athletic Performance';
      case FitnessGoal.rehabilitation:
        return 'Rehabilitation';
    }
  }

  String get description {
    switch (this) {
      case FitnessGoal.weightLoss:
        return 'Burn calories and reduce body fat';
      case FitnessGoal.muscleGain:
        return 'Build lean muscle mass';
      case FitnessGoal.endurance:
        return 'Improve cardiovascular fitness';
      case FitnessGoal.strength:
        return 'Increase overall strength';
      case FitnessGoal.flexibility:
        return 'Enhance mobility and flexibility';
      case FitnessGoal.generalHealth:
        return 'Maintain overall wellness';
      case FitnessGoal.stressRelief:
        return 'Reduce stress and improve mood';
      case FitnessGoal.bodyToning:
        return 'Define and sculpt muscles';
      case FitnessGoal.athleticPerformance:
        return 'Enhance sports performance';
      case FitnessGoal.rehabilitation:
        return 'Recover from injury or condition';
    }
  }

  IconData get icon {
    switch (this) {
      case FitnessGoal.weightLoss:
        return Icons.trending_down;
      case FitnessGoal.muscleGain:
        return Icons.fitness_center;
      case FitnessGoal.endurance:
        return Icons.directions_run;
      case FitnessGoal.strength:
        return Icons.sports_gymnastics;
      case FitnessGoal.flexibility:
        return Icons.self_improvement;
      case FitnessGoal.generalHealth:
        return Icons.favorite;
      case FitnessGoal.stressRelief:
        return Icons.spa;
      case FitnessGoal.bodyToning:
        return Icons.accessibility_new;
      case FitnessGoal.athleticPerformance:
        return Icons.sports;
      case FitnessGoal.rehabilitation:
        return Icons.healing;
    }
  }

  Color get color {
    switch (this) {
      case FitnessGoal.weightLoss:
        return const Color(0xFFEF4444);
      case FitnessGoal.muscleGain:
        return const Color(0xFF8B5CF6);
      case FitnessGoal.endurance:
        return const Color(0xFF06B6D4);
      case FitnessGoal.strength:
        return const Color(0xFFFF6B47);
      case FitnessGoal.flexibility:
        return const Color(0xFF4ADE80);
      case FitnessGoal.generalHealth:
        return const Color(0xFFF59E0B);
      case FitnessGoal.stressRelief:
        return const Color(0xFF8B5CF6);
      case FitnessGoal.bodyToning:
        return const Color(0xFFEC4899);
      case FitnessGoal.athleticPerformance:
        return const Color(0xFF3B82F6);
      case FitnessGoal.rehabilitation:
        return const Color(0xFF10B981);
    }
  }
}

/// Goal timeline options
enum GoalTimeline {
  oneMonth,
  threeMonths,
  sixMonths,
  oneYear,
  ongoing;

  String get displayName {
    switch (this) {
      case GoalTimeline.oneMonth:
        return '1 Month';
      case GoalTimeline.threeMonths:
        return '3 Months';
      case GoalTimeline.sixMonths:
        return '6 Months';
      case GoalTimeline.oneYear:
        return '1 Year';
      case GoalTimeline.ongoing:
        return 'Ongoing';
    }
  }
}

/// Overall fitness experience levels
enum FitnessExperience {
  beginner,
  intermediate,
  advanced;

  String get displayName {
    switch (this) {
      case FitnessExperience.beginner:
        return 'Beginner';
      case FitnessExperience.intermediate:
        return 'Intermediate';
      case FitnessExperience.advanced:
        return 'Advanced';
    }
  }

  String get description {
    switch (this) {
      case FitnessExperience.beginner:
        return 'New to fitness or returning after a long break';
      case FitnessExperience.intermediate:
        return 'Regular exercise routine for 6+ months';
      case FitnessExperience.advanced:
        return 'Consistent training for 2+ years';
    }
  }

  Color get color {
    switch (this) {
      case FitnessExperience.beginner:
        return const Color(0xFF4ADE80);
      case FitnessExperience.intermediate:
        return const Color(0xFFF59E0B);
      case FitnessExperience.advanced:
        return const Color(0xFFEF4444);
    }
  }
}

/// Types of workouts
enum WorkoutType {
  cardio,
  strength,
  yoga,
  pilates,
  hiit,
  crossfit,
  running,
  cycling,
  swimming,
  dancing,
  martialArts,
  sports,
  stretching,
  calisthenics;

  String get displayName {
    switch (this) {
      case WorkoutType.cardio:
        return 'Cardio';
      case WorkoutType.strength:
        return 'Strength Training';
      case WorkoutType.yoga:
        return 'Yoga';
      case WorkoutType.pilates:
        return 'Pilates';
      case WorkoutType.hiit:
        return 'HIIT';
      case WorkoutType.crossfit:
        return 'CrossFit';
      case WorkoutType.running:
        return 'Running';
      case WorkoutType.cycling:
        return 'Cycling';
      case WorkoutType.swimming:
        return 'Swimming';
      case WorkoutType.dancing:
        return 'Dancing';
      case WorkoutType.martialArts:
        return 'Martial Arts';
      case WorkoutType.sports:
        return 'Sports';
      case WorkoutType.stretching:
        return 'Stretching';
      case WorkoutType.calisthenics:
        return 'Calisthenics';
    }
  }

  IconData get icon {
    switch (this) {
      case WorkoutType.cardio:
        return Icons.favorite;
      case WorkoutType.strength:
        return Icons.fitness_center;
      case WorkoutType.yoga:
        return Icons.self_improvement;
      case WorkoutType.pilates:
        return Icons.accessibility_new;
      case WorkoutType.hiit:
        return Icons.flash_on;
      case WorkoutType.crossfit:
        return Icons.sports_gymnastics;
      case WorkoutType.running:
        return Icons.directions_run;
      case WorkoutType.cycling:
        return Icons.directions_bike;
      case WorkoutType.swimming:
        return Icons.pool;
      case WorkoutType.dancing:
        return Icons.music_note;
      case WorkoutType.martialArts:
        return Icons.sports_kabaddi;
      case WorkoutType.sports:
        return Icons.sports_soccer;
      case WorkoutType.stretching:
        return Icons.accessibility;
      case WorkoutType.calisthenics:
        return Icons.sports_gymnastics;
    }
  }
}

/// Days of the week
enum DayOfWeek {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday;

  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Monday';
      case DayOfWeek.tuesday:
        return 'Tuesday';
      case DayOfWeek.wednesday:
        return 'Wednesday';
      case DayOfWeek.thursday:
        return 'Thursday';
      case DayOfWeek.friday:
        return 'Friday';
      case DayOfWeek.saturday:
        return 'Saturday';
      case DayOfWeek.sunday:
        return 'Sunday';
    }
  }

  String get shortName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Mon';
      case DayOfWeek.tuesday:
        return 'Tue';
      case DayOfWeek.wednesday:
        return 'Wed';
      case DayOfWeek.thursday:
        return 'Thu';
      case DayOfWeek.friday:
        return 'Fri';
      case DayOfWeek.saturday:
        return 'Sat';
      case DayOfWeek.sunday:
        return 'Sun';
    }
  }
}

/// Workout intensity preferences
enum IntensityPreference {
  low,
  moderate,
  high,
  varied;

  String get displayName {
    switch (this) {
      case IntensityPreference.low:
        return 'Low Intensity';
      case IntensityPreference.moderate:
        return 'Moderate Intensity';
      case IntensityPreference.high:
        return 'High Intensity';
      case IntensityPreference.varied:
        return 'Varied Intensity';
    }
  }

  String get description {
    switch (this) {
      case IntensityPreference.low:
        return 'Gentle, easy-paced workouts';
      case IntensityPreference.moderate:
        return 'Balanced, sustainable intensity';
      case IntensityPreference.high:
        return 'Challenging, high-energy workouts';
      case IntensityPreference.varied:
        return 'Mix of different intensities';
    }
  }
}

/// Primary workout locations
enum WorkoutLocation {
  home,
  gym,
  outdoor,
  mixed;

  String get displayName {
    switch (this) {
      case WorkoutLocation.home:
        return 'Home';
      case WorkoutLocation.gym:
        return 'Gym';
      case WorkoutLocation.outdoor:
        return 'Outdoor';
      case WorkoutLocation.mixed:
        return 'Mixed';
    }
  }

  String get description {
    switch (this) {
      case WorkoutLocation.home:
        return 'Workout from the comfort of home';
      case WorkoutLocation.gym:
        return 'Access to gym equipment and facilities';
      case WorkoutLocation.outdoor:
        return 'Fresh air and natural environment';
      case WorkoutLocation.mixed:
        return 'Combination of different locations';
    }
  }

  IconData get icon {
    switch (this) {
      case WorkoutLocation.home:
        return Icons.home;
      case WorkoutLocation.gym:
        return Icons.fitness_center;
      case WorkoutLocation.outdoor:
        return Icons.nature;
      case WorkoutLocation.mixed:
        return Icons.shuffle;
    }
  }
}

/// Available equipment types
enum Equipment {
  none,
  dumbbells,
  barbells,
  kettlebells,
  resistanceBands,
  pullupBar,
  yogaMat,
  stabilityBall,
  foamRoller,
  jumpRope,
  treadmill,
  stationaryBike,
  elliptical,
  benchPress,
  squat_rack,
  cableSystem,
  medicine_ball,
  bosuBall;

  String get displayName {
    switch (this) {
      case Equipment.none:
        return 'No Equipment';
      case Equipment.dumbbells:
        return 'Dumbbells';
      case Equipment.barbells:
        return 'Barbells';
      case Equipment.kettlebells:
        return 'Kettlebells';
      case Equipment.resistanceBands:
        return 'Resistance Bands';
      case Equipment.pullupBar:
        return 'Pull-up Bar';
      case Equipment.yogaMat:
        return 'Yoga Mat';
      case Equipment.stabilityBall:
        return 'Stability Ball';
      case Equipment.foamRoller:
        return 'Foam Roller';
      case Equipment.jumpRope:
        return 'Jump Rope';
      case Equipment.treadmill:
        return 'Treadmill';
      case Equipment.stationaryBike:
        return 'Stationary Bike';
      case Equipment.elliptical:
        return 'Elliptical';
      case Equipment.benchPress:
        return 'Bench Press';
      case Equipment.squat_rack:
        return 'Squat Rack';
      case Equipment.cableSystem:
        return 'Cable System';
      case Equipment.medicine_ball:
        return 'Medicine Ball';
      case Equipment.bosuBall:
        return 'BOSU Ball';
    }
  }

  IconData get icon {
    switch (this) {
      case Equipment.none:
        return Icons.not_interested;
      case Equipment.dumbbells:
        return Icons.fitness_center;
      case Equipment.barbells:
        return Icons.fitness_center;
      case Equipment.kettlebells:
        return Icons.sports_gymnastics;
      case Equipment.resistanceBands:
        return Icons.linear_scale;
      case Equipment.pullupBar:
        return Icons.horizontal_rule;
      case Equipment.yogaMat:
        return Icons.crop_landscape;
      case Equipment.stabilityBall:
        return Icons.circle;
      case Equipment.foamRoller:
        return Icons.straighten;
      case Equipment.jumpRope:
        return Icons.timeline;
      case Equipment.treadmill:
        return Icons.directions_run;
      case Equipment.stationaryBike:
        return Icons.directions_bike;
      case Equipment.elliptical:
        return Icons.fitness_center;
      case Equipment.benchPress:
        return Icons.weekend;
      case Equipment.squat_rack:
        return Icons.fitness_center;
      case Equipment.cableSystem:
        return Icons.cable;
      case Equipment.medicine_ball:
        return Icons.sports_volleyball;
      case Equipment.bosuBall:
        return Icons.circle_outlined;
    }
  }
}

/// Schedule flexibility levels
enum ScheduleFlexibility {
  veryFlexible,
  somewhatFlexible,
  limitedFlexibility,
  veryLimited;

  String get displayName {
    switch (this) {
      case ScheduleFlexibility.veryFlexible:
        return 'Very Flexible';
      case ScheduleFlexibility.somewhatFlexible:
        return 'Somewhat Flexible';
      case ScheduleFlexibility.limitedFlexibility:
        return 'Limited Flexibility';
      case ScheduleFlexibility.veryLimited:
        return 'Very Limited';
    }
  }

  String get description {
    switch (this) {
      case ScheduleFlexibility.veryFlexible:
        return 'Can workout anytime, any day';
      case ScheduleFlexibility.somewhatFlexible:
        return 'Some flexibility in timing';
      case ScheduleFlexibility.limitedFlexibility:
        return 'Prefer consistent schedule';
      case ScheduleFlexibility.veryLimited:
        return 'Very specific time requirements';
    }
  }
}

/// Health conditions that may affect workouts
enum HealthCondition {
  none,
  heartCondition,
  highBloodPressure,
  diabetes,
  asthma,
  arthritis,
  backProblems,
  kneeProblems,
  pregnancy,
  recentSurgery,
  other;

  String get displayName {
    switch (this) {
      case HealthCondition.none:
        return 'None';
      case HealthCondition.heartCondition:
        return 'Heart Condition';
      case HealthCondition.highBloodPressure:
        return 'High Blood Pressure';
      case HealthCondition.diabetes:
        return 'Diabetes';
      case HealthCondition.asthma:
        return 'Asthma';
      case HealthCondition.arthritis:
        return 'Arthritis';
      case HealthCondition.backProblems:
        return 'Back Problems';
      case HealthCondition.kneeProblems:
        return 'Knee Problems';
      case HealthCondition.pregnancy:
        return 'Pregnancy';
      case HealthCondition.recentSurgery:
        return 'Recent Surgery';
      case HealthCondition.other:
        return 'Other';
    }
  }
}

/// Physical limitations
enum PhysicalLimitation {
  none,
  lowerBackPain,
  kneeIssues,
  shoulderIssues,
  neckIssues,
  ankleIssues,
  wristIssues,
  balanceIssues,
  mobilityIssues,
  other;

  String get displayName {
    switch (this) {
      case PhysicalLimitation.none:
        return 'None';
      case PhysicalLimitation.lowerBackPain:
        return 'Lower Back Pain';
      case PhysicalLimitation.kneeIssues:
        return 'Knee Issues';
      case PhysicalLimitation.shoulderIssues:
        return 'Shoulder Issues';
      case PhysicalLimitation.neckIssues:
        return 'Neck Issues';
      case PhysicalLimitation.ankleIssues:
        return 'Ankle Issues';
      case PhysicalLimitation.wristIssues:
        return 'Wrist Issues';
      case PhysicalLimitation.balanceIssues:
        return 'Balance Issues';
      case PhysicalLimitation.mobilityIssues:
        return 'Mobility Issues';
      case PhysicalLimitation.other:
        return 'Other';
    }
  }
}

/// Motivation factors
enum MotivationFactor {
  healthImprovement,
  weightManagement,
  stressRelief,
  energyBoost,
  sleepQuality,
  confidence,
  socialConnection,
  competition,
  personalChallenge,
  appearance,
  longevity,
  mentalHealth;

  String get displayName {
    switch (this) {
      case MotivationFactor.healthImprovement:
        return 'Health Improvement';
      case MotivationFactor.weightManagement:
        return 'Weight Management';
      case MotivationFactor.stressRelief:
        return 'Stress Relief';
      case MotivationFactor.energyBoost:
        return 'Energy Boost';
      case MotivationFactor.sleepQuality:
        return 'Better Sleep';
      case MotivationFactor.confidence:
        return 'Confidence';
      case MotivationFactor.socialConnection:
        return 'Social Connection';
      case MotivationFactor.competition:
        return 'Competition';
      case MotivationFactor.personalChallenge:
        return 'Personal Challenge';
      case MotivationFactor.appearance:
        return 'Appearance';
      case MotivationFactor.longevity:
        return 'Longevity';
      case MotivationFactor.mentalHealth:
        return 'Mental Health';
    }
  }

  IconData get icon {
    switch (this) {
      case MotivationFactor.healthImprovement:
        return Icons.favorite;
      case MotivationFactor.weightManagement:
        return Icons.monitor_weight;
      case MotivationFactor.stressRelief:
        return Icons.spa;
      case MotivationFactor.energyBoost:
        return Icons.bolt;
      case MotivationFactor.sleepQuality:
        return Icons.bedtime;
      case MotivationFactor.confidence:
        return Icons.psychology;
      case MotivationFactor.socialConnection:
        return Icons.people;
      case MotivationFactor.competition:
        return Icons.emoji_events;
      case MotivationFactor.personalChallenge:
        return Icons.trending_up;
      case MotivationFactor.appearance:
        return Icons.visibility;
      case MotivationFactor.longevity:
        return Icons.access_time;
      case MotivationFactor.mentalHealth:
        return Icons.psychology_alt;
    }
  }
}

/// Tracking preferences
enum TrackingPreference {
  detailed,
  simple,
  minimal,
  none;

  String get displayName {
    switch (this) {
      case TrackingPreference.detailed:
        return 'Detailed Tracking';
      case TrackingPreference.simple:
        return 'Simple Tracking';
      case TrackingPreference.minimal:
        return 'Minimal Tracking';
      case TrackingPreference.none:
        return 'No Tracking';
    }
  }

  String get description {
    switch (this) {
      case TrackingPreference.detailed:
        return 'Track everything: reps, sets, weight, time, calories';
      case TrackingPreference.simple:
        return 'Track basic metrics: duration and completion';
      case TrackingPreference.minimal:
        return 'Just mark workouts as complete';
      case TrackingPreference.none:
        return 'No tracking, just workout';
    }
  }
}