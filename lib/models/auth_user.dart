class AuthUser {
  final String id;
  final String email;
  final String displayName;
  final String? photoURL;
  final bool isEmailVerified;

  const AuthUser({
    required this.id,
    required this.email,
    required this.displayName,
    this.photoURL,
    required this.isEmailVerified,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) => AuthUser(
        id: json['id'] as String,
        email: json['email'] as String,
        displayName: json['displayName'] as String,
        photoURL: json['photoURL'] as String?,
        isEmailVerified: json['isEmailVerified'] as bool,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'email': email,
        'displayName': displayName,
        'photoURL': photoURL,
        'isEmailVerified': isEmailVerified,
      };
}

class AuthState {
  final AuthUser? user;
  final bool isLoading;
  final String? error;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
  });

  bool get isAuthenticated => user != null;

  AuthState copyWith({
    AuthUser? user,
    bool? isLoading,
    String? error,
  }) =>
      AuthState(
        user: user ?? this.user,
        isLoading: isLoading ?? this.isLoading,
        error: error ?? this.error,
      );
}