/// Supabase Profile model matching the database schema
class SupabaseProfile {
  final String id;
  final String? email;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Basic Info
  final String? displayName;
  final String? gender;
  final int? age;
  final double? height;
  final String heightUnit;
  final double? weight;
  final String weightUnit;
  
  // Fitness Goals & Preferences
  final List<String> primaryGoal;
  final String? fitnessGoals;
  final List<String> fitnessGoalsArray;
  final String? fitnessGoalPrimary;
  final List<String> fitnessGoalsOrder;
  
  // Fitness Levels
  final String? cardioLevel;
  final String? weightliftingLevel;
  final int? cardioFitnessLevel;
  final int? weightliftingFitnessLevel;
  final String? cardioLevelDescription;
  final String? weightliftingLevelDescription;
  final String? fitnessExperience;
  final int? fitnessLevel;
  final String? trainingExperienceLevel;
  
  // Equipment & Environment
  final List<String> equipment;
  final List<String> workoutEnvironment;
  
  // Workout Preferences
  final List<String> workoutDays;
  final String? workoutDuration;
  final String? workoutFrequency;
  final int? workoutFrequencyDays;
  final int? workoutDurationMinutes;
  final int? workoutFrequencyNumber;
  final String? workoutDurationPreference;
  final int? preferredWorkoutDaysCount;
  final String? preferredWorkoutDuration;
  final String? scheduleFlexibility;
  
  // Health & Lifestyle
  final List<String> healthConditions;
  final List<String> dietaryRestrictions;
  final List<String> dietPreferences;
  final List<String> physicalLimitations;
  final List<String> exercisesToAvoid;
  final List<String> excludedExercises;
  final List<String> exercisePreferences;
  final List<String> supplements;
  final bool? takingSupplements;
  final int? mealsPerDay;
  final int? caloricGoal;
  final String? sleepQuality;
  final String? additionalHealthInfo;
  
  // Sports & Activities
  final String? sportActivity;
  final String? specificSportActivity;
  final String? sportOfChoice;
  
  // Completion Status
  final bool hasCompletedPreferences;
  final bool onboardingCompleted;
  final bool fitnessAssessmentCompleted;
  
  // Additional Data
  final String? fitnessGuide;
  final String? additionalNotes;
  final Map<String, dynamic>? metadata;

  const SupabaseProfile({
    required this.id,
    this.email,
    required this.createdAt,
    required this.updatedAt,
    this.displayName,
    this.gender,
    this.age,
    this.height,
    this.heightUnit = 'cm',
    this.weight,
    this.weightUnit = 'kg',
    this.primaryGoal = const [],
    this.fitnessGoals,
    this.fitnessGoalsArray = const [],
    this.fitnessGoalPrimary,
    this.fitnessGoalsOrder = const [],
    this.cardioLevel,
    this.weightliftingLevel,
    this.cardioFitnessLevel,
    this.weightliftingFitnessLevel,
    this.cardioLevelDescription,
    this.weightliftingLevelDescription,
    this.fitnessExperience,
    this.fitnessLevel,
    this.trainingExperienceLevel,
    this.equipment = const [],
    this.workoutEnvironment = const [],
    this.workoutDays = const [],
    this.workoutDuration,
    this.workoutFrequency,
    this.workoutFrequencyDays,
    this.workoutDurationMinutes,
    this.workoutFrequencyNumber,
    this.workoutDurationPreference,
    this.preferredWorkoutDaysCount,
    this.preferredWorkoutDuration,
    this.scheduleFlexibility,
    this.healthConditions = const [],
    this.dietaryRestrictions = const [],
    this.dietPreferences = const [],
    this.physicalLimitations = const [],
    this.exercisesToAvoid = const [],
    this.excludedExercises = const [],
    this.exercisePreferences = const [],
    this.supplements = const [],
    this.takingSupplements,
    this.mealsPerDay,
    this.caloricGoal,
    this.sleepQuality,
    this.additionalHealthInfo,
    this.sportActivity,
    this.specificSportActivity,
    this.sportOfChoice,
    this.hasCompletedPreferences = false,
    this.onboardingCompleted = false,
    this.fitnessAssessmentCompleted = false,
    this.fitnessGuide,
    this.additionalNotes,
    this.metadata,
  });

  factory SupabaseProfile.fromJson(Map<String, dynamic> json) {
    return SupabaseProfile(
      id: json['id'] as String,
      email: json['email'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      displayName: json['display_name'] as String?,
      gender: json['gender'] as String?,
      age: json['age'] as int?,
      height: json['height'] != null ? double.parse(json['height'].toString()) : null,
      heightUnit: json['height_unit'] as String? ?? 'cm',
      weight: json['weight'] != null ? double.parse(json['weight'].toString()) : null,
      weightUnit: json['weight_unit'] as String? ?? 'kg',
      primaryGoal: _parseStringArray(json['primarygoal']),
      fitnessGoals: json['fitnessgoals'] as String?,
      fitnessGoalsArray: _parseStringArray(json['fitness_goals_array']),
      fitnessGoalPrimary: json['fitness_goal_primary'] as String?,
      fitnessGoalsOrder: _parseStringArray(json['fitness_goals_order']),
      cardioLevel: json['cardiolevel'] as String?,
      weightliftingLevel: json['weightliftinglevel'] as String?,
      cardioFitnessLevel: json['cardio_fitness_level'] as int?,
      weightliftingFitnessLevel: json['weightlifting_fitness_level'] as int?,
      cardioLevelDescription: json['cardio_level_description'] as String?,
      weightliftingLevelDescription: json['weightlifting_level_description'] as String?,
      fitnessExperience: json['fitness_experience'] as String?,
      fitnessLevel: json['fitness_level'] as int?,
      trainingExperienceLevel: json['training_experience_level'] as String?,
      equipment: _parseStringArray(json['equipment']),
      workoutEnvironment: _parseStringArray(json['workout_environment']),
      workoutDays: _parseStringArray(json['workout_days'] ?? json['workoutdays']),
      workoutDuration: json['workoutduration'] as String?,
      workoutFrequency: json['workoutfrequency'] as String?,
      workoutFrequencyDays: json['workout_frequency_days'] as int?,
      workoutDurationMinutes: json['workout_duration'] as int?,
      workoutFrequencyNumber: json['workout_frequency'] as int?,
      workoutDurationPreference: json['workout_duration_preference'] as String?,
      preferredWorkoutDaysCount: json['preferred_workout_days_count'] as int?,
      preferredWorkoutDuration: json['preferred_workout_duration'] as String?,
      scheduleFlexibility: json['schedule_flexibility'] as String?,
      healthConditions: _parseStringArray(json['health_conditions']),
      dietaryRestrictions: _parseStringArray(json['dietary_restrictions']),
      dietPreferences: _parseStringArray(json['diet_preferences']),
      physicalLimitations: _parseStringArray(json['physical_limitations']),
      exercisesToAvoid: _parseStringArray(json['exercises_to_avoid']),
      excludedExercises: _parseStringArray(json['excluded_exercises']),
      exercisePreferences: _parseStringArray(json['exercise_preferences']),
      supplements: _parseStringArray(json['supplements']),
      takingSupplements: json['taking_supplements'] as bool?,
      mealsPerDay: json['meals_per_day'] as int?,
      caloricGoal: json['caloric_goal'] as int?,
      sleepQuality: json['sleep_quality'] as String?,
      additionalHealthInfo: json['additional_health_info'] as String?,
      sportActivity: json['sport_activity'] as String?,
      specificSportActivity: json['specific_sport_activity'] as String?,
      sportOfChoice: json['sport_of_choice'] as String?,
      hasCompletedPreferences: json['has_completed_preferences'] as bool? ?? false,
      onboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      fitnessAssessmentCompleted: json['fitness_assessment_completed'] as bool? ?? false,
      fitnessGuide: json['fitness_guide'] as String?,
      additionalNotes: json['additional_notes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  static List<String> _parseStringArray(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'display_name': displayName,
      'gender': gender,
      'age': age,
      'height': height,
      'height_unit': heightUnit,
      'weight': weight,
      'weight_unit': weightUnit,
      'primarygoal': primaryGoal,
      'fitnessgoals': fitnessGoals,
      'fitness_goals_array': fitnessGoalsArray,
      'fitness_goal_primary': fitnessGoalPrimary,
      'fitness_goals_order': fitnessGoalsOrder,
      'cardiolevel': cardioLevel,
      'weightliftinglevel': weightliftingLevel,
      'cardio_fitness_level': cardioFitnessLevel,
      'weightlifting_fitness_level': weightliftingFitnessLevel,
      'cardio_level_description': cardioLevelDescription,
      'weightlifting_level_description': weightliftingLevelDescription,
      'fitness_experience': fitnessExperience,
      'fitness_level': fitnessLevel,
      'training_experience_level': trainingExperienceLevel,
      'equipment': equipment,
      'workout_environment': workoutEnvironment,
      'workout_days': workoutDays,
      'workoutduration': workoutDuration,
      'workoutfrequency': workoutFrequency,
      'workout_frequency_days': workoutFrequencyDays,
      'workout_duration': workoutDurationMinutes,
      'workout_frequency': workoutFrequencyNumber,
      'workout_duration_preference': workoutDurationPreference,
      'preferred_workout_days_count': preferredWorkoutDaysCount,
      'preferred_workout_duration': preferredWorkoutDuration,
      'schedule_flexibility': scheduleFlexibility,
      'health_conditions': healthConditions,
      'dietary_restrictions': dietaryRestrictions,
      'diet_preferences': dietPreferences,
      'physical_limitations': physicalLimitations,
      'exercises_to_avoid': exercisesToAvoid,
      'excluded_exercises': excludedExercises,
      'exercise_preferences': exercisePreferences,
      'supplements': supplements,
      'taking_supplements': takingSupplements,
      'meals_per_day': mealsPerDay,
      'caloric_goal': caloricGoal,
      'sleep_quality': sleepQuality,
      'additional_health_info': additionalHealthInfo,
      'sport_activity': sportActivity,
      'specific_sport_activity': specificSportActivity,
      'sport_of_choice': sportOfChoice,
      'has_completed_preferences': hasCompletedPreferences,
      'onboarding_completed': onboardingCompleted,
      'fitness_assessment_completed': fitnessAssessmentCompleted,
      'fitness_guide': fitnessGuide,
      'additional_notes': additionalNotes,
      'metadata': metadata,
    };
  }

  SupabaseProfile copyWith({
    String? displayName,
    String? gender,
    int? age,
    double? height,
    String? heightUnit,
    double? weight,
    String? weightUnit,
    List<String>? primaryGoal,
    String? fitnessGoals,
    List<String>? fitnessGoalsArray,
    String? fitnessGoalPrimary,
    List<String>? equipment,
    List<String>? workoutDays,
    String? workoutDuration,
    String? workoutFrequency,
    int? workoutFrequencyDays,
    int? workoutDurationMinutes,
    int? workoutFrequencyNumber,
    bool? hasCompletedPreferences,
    bool? onboardingCompleted,
    Map<String, dynamic>? metadata,
  }) {
    return SupabaseProfile(
      id: id,
      email: email,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      displayName: displayName ?? this.displayName,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      height: height ?? this.height,
      heightUnit: heightUnit ?? this.heightUnit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      primaryGoal: primaryGoal ?? this.primaryGoal,
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      fitnessGoalsArray: fitnessGoalsArray ?? this.fitnessGoalsArray,
      fitnessGoalPrimary: fitnessGoalPrimary ?? this.fitnessGoalPrimary,
      equipment: equipment ?? this.equipment,
      workoutDays: workoutDays ?? this.workoutDays,
      workoutDuration: workoutDuration ?? this.workoutDuration,
      workoutFrequency: workoutFrequency ?? this.workoutFrequency,
      workoutFrequencyDays: workoutFrequencyDays ?? this.workoutFrequencyDays,
      workoutDurationMinutes: workoutDurationMinutes ?? this.workoutDurationMinutes,
      workoutFrequencyNumber: workoutFrequencyNumber ?? this.workoutFrequencyNumber,
      hasCompletedPreferences: hasCompletedPreferences ?? this.hasCompletedPreferences,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      metadata: metadata ?? this.metadata,
      // Copy all other fields
      fitnessGoalsOrder: fitnessGoalsOrder,
      cardioLevel: cardioLevel,
      weightliftingLevel: weightliftingLevel,
      cardioFitnessLevel: cardioFitnessLevel,
      weightliftingFitnessLevel: weightliftingFitnessLevel,
      cardioLevelDescription: cardioLevelDescription,
      weightliftingLevelDescription: weightliftingLevelDescription,
      fitnessExperience: fitnessExperience,
      fitnessLevel: fitnessLevel,
      trainingExperienceLevel: trainingExperienceLevel,
      workoutEnvironment: workoutEnvironment,
      workoutDurationPreference: workoutDurationPreference,
      preferredWorkoutDaysCount: preferredWorkoutDaysCount,
      preferredWorkoutDuration: preferredWorkoutDuration,
      scheduleFlexibility: scheduleFlexibility,
      healthConditions: healthConditions,
      dietaryRestrictions: dietaryRestrictions,
      dietPreferences: dietPreferences,
      physicalLimitations: physicalLimitations,
      exercisesToAvoid: exercisesToAvoid,
      excludedExercises: excludedExercises,
      exercisePreferences: exercisePreferences,
      supplements: supplements,
      takingSupplements: takingSupplements,
      mealsPerDay: mealsPerDay,
      caloricGoal: caloricGoal,
      sleepQuality: sleepQuality,
      additionalHealthInfo: additionalHealthInfo,
      sportActivity: sportActivity,
      specificSportActivity: specificSportActivity,
      sportOfChoice: sportOfChoice,
      fitnessAssessmentCompleted: fitnessAssessmentCompleted,
      fitnessGuide: fitnessGuide,
      additionalNotes: additionalNotes,
    );
  }
}