import 'package:equatable/equatable.dart';
import 'package:openfit/models/enhanced_workout.dart';

/// Enhanced workout session model for tracking completed workouts
class WorkoutSessionEnhanced extends Equatable {
  final String id;
  final String userId;
  final String? workoutId;
  final DateTime startedAt;
  final DateTime? completedAt;
  final int? durationMinutes;
  final int? caloriesBurned;
  final int? rating; // 1-5 stars
  final String? notes;
  final bool isCompleted;
  final EnhancedWorkout? workout; // Populated when joined
  final List<SessionExercise> exercises;
  final DateTime createdAt;

  const WorkoutSessionEnhanced({
    required this.id,
    required this.userId,
    this.workoutId,
    required this.startedAt,
    this.completedAt,
    this.durationMinutes,
    this.caloriesBurned,
    this.rating,
    this.notes,
    this.isCompleted = false,
    this.workout,
    this.exercises = const [],
    required this.createdAt,
  });

  factory WorkoutSessionEnhanced.fromJson(Map<String, dynamic> json) {
    return WorkoutSessionEnhanced(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      workoutId: json['workout_id'] as String?,
      startedAt: DateTime.parse(json['started_at'] as String),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'] as String) : null,
      durationMinutes: json['duration_minutes'] as int?,
      caloriesBurned: json['calories_burned'] as int?,
      rating: json['rating'] as int?,
      notes: json['notes'] as String?,
      isCompleted: json['is_completed'] as bool? ?? false,
      workout: json['workout'] != null 
          ? EnhancedWorkout.fromJson(json['workout']) : null,
      exercises: json['session_exercises'] != null
          ? (json['session_exercises'] as List)
              .map((e) => SessionExercise.fromJson(e))
              .toList()
          : [],
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'workout_id': workoutId,
      'started_at': startedAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'duration_minutes': durationMinutes,
      'calories_burned': caloriesBurned,
      'rating': rating,
      'notes': notes,
      'is_completed': isCompleted,
    };
  }

  WorkoutSessionEnhanced copyWith({
    String? id,
    String? userId,
    String? workoutId,
    DateTime? startedAt,
    DateTime? completedAt,
    int? durationMinutes,
    int? caloriesBurned,
    int? rating,
    String? notes,
    bool? isCompleted,
    EnhancedWorkout? workout,
    List<SessionExercise>? exercises,
    DateTime? createdAt,
  }) {
    return WorkoutSessionEnhanced(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      workoutId: workoutId ?? this.workoutId,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      caloriesBurned: caloriesBurned ?? this.caloriesBurned,
      rating: rating ?? this.rating,
      notes: notes ?? this.notes,
      isCompleted: isCompleted ?? this.isCompleted,
      workout: workout ?? this.workout,
      exercises: exercises ?? this.exercises,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get workout duration in a readable format
  String get formattedDuration {
    if (durationMinutes == null) return 'Unknown';
    if (durationMinutes! < 60) return '${durationMinutes}min';
    
    final hours = durationMinutes! ~/ 60;
    final minutes = durationMinutes! % 60;
    return minutes > 0 ? '${hours}h ${minutes}min' : '${hours}h';
  }

  /// Get completion percentage based on exercises completed
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return completedExercises / exercises.length;
  }

  /// Check if session is currently active (started but not completed)
  bool get isActive => !isCompleted && completedAt == null;

  /// Get total sets completed across all exercises
  int get totalSetsCompleted {
    return exercises.fold(0, (sum, exercise) => sum + (exercise.setsCompleted ?? 0));
  }

  /// Get total reps completed across all exercises
  int get totalRepsCompleted {
    return exercises.fold(0, (sum, exercise) => sum + (exercise.repsCompleted ?? 0));
  }

  @override
  List<Object?> get props => [
    id, userId, workoutId, startedAt, completedAt, durationMinutes,
    caloriesBurned, rating, notes, isCompleted, workout, exercises, createdAt,
  ];
}

/// Individual exercise performance within a workout session
class SessionExercise extends Equatable {
  final String id;
  final String sessionId;
  final String exerciseId;
  final int? setsCompleted;
  final int? repsCompleted;
  final double? weightUsedKg;
  final int? durationSeconds;
  final int? restSeconds;
  final String? notes;
  final bool isCompleted;
  final EnhancedExercise? exercise; // Populated when joined
  final DateTime createdAt;

  const SessionExercise({
    required this.id,
    required this.sessionId,
    required this.exerciseId,
    this.setsCompleted,
    this.repsCompleted,
    this.weightUsedKg,
    this.durationSeconds,
    this.restSeconds,
    this.notes,
    this.isCompleted = false,
    this.exercise,
    required this.createdAt,
  });

  factory SessionExercise.fromJson(Map<String, dynamic> json) {
    return SessionExercise(
      id: json['id'] as String,
      sessionId: json['session_id'] as String,
      exerciseId: json['exercise_id'] as String,
      setsCompleted: json['sets_completed'] as int?,
      repsCompleted: json['reps_completed'] as int?,
      weightUsedKg: json['weight_used_kg'] != null 
          ? (json['weight_used_kg'] as num).toDouble() : null,
      durationSeconds: json['duration_seconds'] as int?,
      restSeconds: json['rest_seconds'] as int?,
      notes: json['notes'] as String?,
      isCompleted: json['is_completed'] as bool? ?? false,
      exercise: json['exercise'] != null 
          ? EnhancedExercise.fromJson(json['exercise']) : null,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'session_id': sessionId,
      'exercise_id': exerciseId,
      'sets_completed': setsCompleted,
      'reps_completed': repsCompleted,
      'weight_used_kg': weightUsedKg,
      'duration_seconds': durationSeconds,
      'rest_seconds': restSeconds,
      'notes': notes,
      'is_completed': isCompleted,
    };
  }

  SessionExercise copyWith({
    String? id,
    String? sessionId,
    String? exerciseId,
    int? setsCompleted,
    int? repsCompleted,
    double? weightUsedKg,
    int? durationSeconds,
    int? restSeconds,
    String? notes,
    bool? isCompleted,
    EnhancedExercise? exercise,
    DateTime? createdAt,
  }) {
    return SessionExercise(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      exerciseId: exerciseId ?? this.exerciseId,
      setsCompleted: setsCompleted ?? this.setsCompleted,
      repsCompleted: repsCompleted ?? this.repsCompleted,
      weightUsedKg: weightUsedKg ?? this.weightUsedKg,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      restSeconds: restSeconds ?? this.restSeconds,
      notes: notes ?? this.notes,
      isCompleted: isCompleted ?? this.isCompleted,
      exercise: exercise ?? this.exercise,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get formatted duration string
  String get formattedDuration {
    if (durationSeconds == null) return 'N/A';
    if (durationSeconds! < 60) return '${durationSeconds}s';
    
    final minutes = durationSeconds! ~/ 60;
    final seconds = durationSeconds! % 60;
    return seconds > 0 ? '${minutes}m ${seconds}s' : '${minutes}m';
  }

  @override
  List<Object?> get props => [
    id, sessionId, exerciseId, setsCompleted, repsCompleted,
    weightUsedKg, durationSeconds, restSeconds, notes, isCompleted,
    exercise, createdAt,
  ];
}