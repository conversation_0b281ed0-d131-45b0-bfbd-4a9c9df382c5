class WorkoutSession {
  final String id;
  final String workoutId;
  final String workoutName;
  final String workoutType;
  final String imageUrl;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration totalDuration;
  final Duration currentDuration;
  final List<ExerciseSession> exercises;
  final WorkoutSessionStatus status;
  final int caloriesBurned;
  final double completionPercentage;
  final double rating;

  const WorkoutSession({
    required this.id,
    required this.workoutId,
    required this.workoutName,
    required this.workoutType,
    required this.imageUrl,
    required this.startTime,
    this.endTime,
    required this.totalDuration,
    required this.currentDuration,
    required this.exercises,
    required this.status,
    required this.caloriesBurned,
    required this.completionPercentage,
    required this.rating,
  });

  WorkoutSession copyWith({
    String? id,
    String? workoutId,
    String? workoutName,
    String? workoutType,
    String? imageUrl,
    DateTime? startTime,
    DateTime? endTime,
    Duration? totalDuration,
    Duration? currentDuration,
    List<ExerciseSession>? exercises,
    WorkoutSessionStatus? status,
    int? caloriesBurned,
    double? completionPercentage,
    double? rating,
  }) {
    return WorkoutSession(
      id: id ?? this.id,
      workoutId: workoutId ?? this.workoutId,
      workoutName: workoutName ?? this.workoutName,
      workoutType: workoutType ?? this.workoutType,
      imageUrl: imageUrl ?? this.imageUrl,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalDuration: totalDuration ?? this.totalDuration,
      currentDuration: currentDuration ?? this.currentDuration,
      exercises: exercises ?? this.exercises,
      status: status ?? this.status,
      caloriesBurned: caloriesBurned ?? this.caloriesBurned,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      rating: rating ?? this.rating,
    );
  }

  // Backwards compatibility
  DateTime get dateCompleted => endTime ?? startTime;
  int get actualDuration => currentDuration.inMinutes;
  List<String> get completedExercises => exercises
      .where((e) => e.isCompleted)
      .map((e) => e.exerciseName)
      .toList();

  factory WorkoutSession.fromMap(Map<String, dynamic> map) => WorkoutSession(
        id: map['id'] ?? '',
        workoutId: map['workoutId'] ?? '',
        workoutName: map['workoutName'] ?? '',
        workoutType: map['workoutType'] ?? 'General',
        imageUrl: map['imageUrl'] ?? '',
        startTime: DateTime.fromMillisecondsSinceEpoch(
            map['startTime'] ?? DateTime.now().millisecondsSinceEpoch),
        endTime: map['endTime'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(map['endTime'])
            : null,
        totalDuration: Duration(minutes: map['totalDuration'] ?? 30),
        currentDuration: Duration(minutes: map['currentDuration'] ?? 0),
        exercises: (map['exercises'] as List<dynamic>?)
            ?.map((e) => ExerciseSession.fromMap(e))
            .toList() ?? [],
        status: WorkoutSessionStatus.values.firstWhere(
          (s) => s.name == map['status'],
          orElse: () => WorkoutSessionStatus.notStarted,
        ),
        caloriesBurned: map['caloriesBurned'] ?? 0,
        completionPercentage: (map['completionPercentage'] ?? 0.0).toDouble(),
        rating: (map['rating'] ?? 0.0).toDouble(),
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'workoutId': workoutId,
        'workoutName': workoutName,
        'workoutType': workoutType,
        'imageUrl': imageUrl,
        'startTime': startTime.millisecondsSinceEpoch,
        'endTime': endTime?.millisecondsSinceEpoch,
        'totalDuration': totalDuration.inMinutes,
        'currentDuration': currentDuration.inMinutes,
        'exercises': exercises.map((e) => e.toMap()).toList(),
        'status': status.name,
        'caloriesBurned': caloriesBurned,
        'completionPercentage': completionPercentage,
        'rating': rating,
      };
}

enum WorkoutSessionStatus {
  notStarted,
  inProgress,
  paused,
  completed,
  cancelled,
}

class ExerciseSession {
  final String id;
  final String exerciseName;
  final String description;
  final Duration duration;
  final int? sets;
  final int? reps;
  final bool isCompleted;
  final String videoUrl;
  final String thumbnailUrl;

  const ExerciseSession({
    required this.id,
    required this.exerciseName,
    required this.description,
    required this.duration,
    this.sets,
    this.reps,
    required this.isCompleted,
    required this.videoUrl,
    required this.thumbnailUrl,
  });

  ExerciseSession copyWith({
    String? id,
    String? exerciseName,
    String? description,
    Duration? duration,
    int? sets,
    int? reps,
    bool? isCompleted,
    String? videoUrl,
    String? thumbnailUrl,
  }) {
    return ExerciseSession(
      id: id ?? this.id,
      exerciseName: exerciseName ?? this.exerciseName,
      description: description ?? this.description,
      duration: duration ?? this.duration,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      isCompleted: isCompleted ?? this.isCompleted,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
    );
  }

  factory ExerciseSession.fromMap(Map<String, dynamic> map) => ExerciseSession(
        id: map['id'] ?? '',
        exerciseName: map['exerciseName'] ?? '',
        description: map['description'] ?? '',
        duration: Duration(seconds: map['duration'] ?? 30),
        sets: map['sets'],
        reps: map['reps'],
        isCompleted: map['isCompleted'] ?? false,
        videoUrl: map['videoUrl'] ?? '',
        thumbnailUrl: map['thumbnailUrl'] ?? '',
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'exerciseName': exerciseName,
        'description': description,
        'duration': duration.inSeconds,
        'sets': sets,
        'reps': reps,
        'isCompleted': isCompleted,
        'videoUrl': videoUrl,
        'thumbnailUrl': thumbnailUrl,
      };
}

class WorkoutHistoryEntry {
  final String id;
  final String workoutName;
  final String workoutType;
  final DateTime date;
  final Duration duration;
  final int caloriesBurned;
  final double completionPercentage;
  final String imageUrl;
  final List<String> exercisesCompleted;

  const WorkoutHistoryEntry({
    required this.id,
    required this.workoutName,
    required this.workoutType,
    required this.date,
    required this.duration,
    required this.caloriesBurned,
    required this.completionPercentage,
    required this.imageUrl,
    required this.exercisesCompleted,
  });
}