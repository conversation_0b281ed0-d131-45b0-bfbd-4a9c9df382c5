class User {
  final String id;
  final String name;
  final String email;
  final int age;
  final double weight; // in kg
  final double height; // in cm
  final String fitnessGoal;
  final int weeklyWorkoutGoal;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.age,
    required this.weight,
    required this.height,
    required this.fitnessGoal,
    required this.weeklyWorkoutGoal,
  });

  factory User.fromMap(Map<String, dynamic> map) => User(
        id: map['id'] ?? '',
        name: map['name'] ?? '',
        email: map['email'] ?? '',
        age: map['age'] ?? 0,
        weight: (map['weight'] ?? 0.0).toDouble(),
        height: (map['height'] ?? 0.0).toDouble(),
        fitnessGoal: map['fitnessGoal'] ?? '',
        weeklyWorkoutGoal: map['weeklyWorkoutGoal'] ?? 0,
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'email': email,
        'age': age,
        'weight': weight,
        'height': height,
        'fitnessGoal': fitnessGoal,
        'weeklyWorkoutGoal': weeklyWorkoutGoal,
      };

  double get bmi => weight / ((height / 100) * (height / 100));
}