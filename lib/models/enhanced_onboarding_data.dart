import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_enums.dart';

/// Enhanced onboarding data model with comprehensive user information
class EnhancedOnboardingData extends Equatable {
  // Personal Information
  final String? fullName;
  final int? age;
  final Gender? gender;
  final double? height;
  final HeightUnit? heightUnit;
  final double? weight;
  final WeightUnit? weightUnit;
  
  // Fitness Goals (Priority Ranked)
  final List<FitnessGoal> primaryGoals;
  final List<FitnessGoal> secondaryGoals;
  final String? specificGoalDetails;
  final GoalTimeline? goalTimeline;
  
  // Experience & Fitness Level
  final FitnessExperience? overallExperience;
  final int? cardioLevel; // 1-10 scale
  final int? strengthLevel; // 1-10 scale
  final int? flexibilityLevel; // 1-10 scale
  final List<String> previousActivities;
  final int? currentWeeklyFrequency;
  
  // Workout Preferences
  final List<WorkoutType> preferredWorkoutTypes;
  final List<DayOfWeek> availableDays;
  final TimeOfDay? preferredWorkoutTime;
  final int? sessionDuration; // minutes
  final int? weeklyFrequency;
  final IntensityPreference? intensityPreference;
  
  // Equipment & Environment
  final WorkoutLocation? primaryLocation;
  final List<Equipment> availableEquipment;
  final double? budgetForEquipment;
  final ScheduleFlexibility? scheduleFlexibility;
  
  // Health & Limitations
  final List<HealthCondition> healthConditions;
  final List<PhysicalLimitation> physicalLimitations;
  final List<String> exercisesToAvoid;
  final String? additionalHealthNotes;
  
  // Motivation & Tracking
  final List<MotivationFactor> motivationFactors;
  final TrackingPreference? trackingPreference;
  final bool wantsReminders;
  final List<TimeOfDay> reminderTimes;
  final bool wantsSocialFeatures;
  
  // Progress tracking
  final int currentStep;
  final bool isCompleted;
  final DateTime? lastUpdated;

  const EnhancedOnboardingData({
    // Personal Information
    this.fullName,
    this.age,
    this.gender,
    this.height,
    this.heightUnit,
    this.weight,
    this.weightUnit,
    
    // Fitness Goals
    this.primaryGoals = const [],
    this.secondaryGoals = const [],
    this.specificGoalDetails,
    this.goalTimeline,
    
    // Experience & Fitness Level
    this.overallExperience,
    this.cardioLevel,
    this.strengthLevel,
    this.flexibilityLevel,
    this.previousActivities = const [],
    this.currentWeeklyFrequency,
    
    // Workout Preferences
    this.preferredWorkoutTypes = const [],
    this.availableDays = const [],
    this.preferredWorkoutTime,
    this.sessionDuration,
    this.weeklyFrequency,
    this.intensityPreference,
    
    // Equipment & Environment
    this.primaryLocation,
    this.availableEquipment = const [],
    this.budgetForEquipment,
    this.scheduleFlexibility,
    
    // Health & Limitations
    this.healthConditions = const [],
    this.physicalLimitations = const [],
    this.exercisesToAvoid = const [],
    this.additionalHealthNotes,
    
    // Motivation & Tracking
    this.motivationFactors = const [],
    this.trackingPreference,
    this.wantsReminders = false,
    this.reminderTimes = const [],
    this.wantsSocialFeatures = false,
    
    // Progress tracking
    this.currentStep = 0,
    this.isCompleted = false,
    this.lastUpdated,
  });

  /// Create a copy with updated fields
  EnhancedOnboardingData copyWith({
    String? fullName,
    int? age,
    Gender? gender,
    double? height,
    HeightUnit? heightUnit,
    double? weight,
    WeightUnit? weightUnit,
    List<FitnessGoal>? primaryGoals,
    List<FitnessGoal>? secondaryGoals,
    String? specificGoalDetails,
    GoalTimeline? goalTimeline,
    FitnessExperience? overallExperience,
    int? cardioLevel,
    int? strengthLevel,
    int? flexibilityLevel,
    List<String>? previousActivities,
    int? currentWeeklyFrequency,
    List<WorkoutType>? preferredWorkoutTypes,
    List<DayOfWeek>? availableDays,
    TimeOfDay? preferredWorkoutTime,
    int? sessionDuration,
    int? weeklyFrequency,
    IntensityPreference? intensityPreference,
    WorkoutLocation? primaryLocation,
    List<Equipment>? availableEquipment,
    double? budgetForEquipment,
    ScheduleFlexibility? scheduleFlexibility,
    List<HealthCondition>? healthConditions,
    List<PhysicalLimitation>? physicalLimitations,
    List<String>? exercisesToAvoid,
    String? additionalHealthNotes,
    List<MotivationFactor>? motivationFactors,
    TrackingPreference? trackingPreference,
    bool? wantsReminders,
    List<TimeOfDay>? reminderTimes,
    bool? wantsSocialFeatures,
    int? currentStep,
    bool? isCompleted,
    DateTime? lastUpdated,
  }) {
    return EnhancedOnboardingData(
      fullName: fullName ?? this.fullName,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      heightUnit: heightUnit ?? this.heightUnit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      primaryGoals: primaryGoals ?? this.primaryGoals,
      secondaryGoals: secondaryGoals ?? this.secondaryGoals,
      specificGoalDetails: specificGoalDetails ?? this.specificGoalDetails,
      goalTimeline: goalTimeline ?? this.goalTimeline,
      overallExperience: overallExperience ?? this.overallExperience,
      cardioLevel: cardioLevel ?? this.cardioLevel,
      strengthLevel: strengthLevel ?? this.strengthLevel,
      flexibilityLevel: flexibilityLevel ?? this.flexibilityLevel,
      previousActivities: previousActivities ?? this.previousActivities,
      currentWeeklyFrequency: currentWeeklyFrequency ?? this.currentWeeklyFrequency,
      preferredWorkoutTypes: preferredWorkoutTypes ?? this.preferredWorkoutTypes,
      availableDays: availableDays ?? this.availableDays,
      preferredWorkoutTime: preferredWorkoutTime ?? this.preferredWorkoutTime,
      sessionDuration: sessionDuration ?? this.sessionDuration,
      weeklyFrequency: weeklyFrequency ?? this.weeklyFrequency,
      intensityPreference: intensityPreference ?? this.intensityPreference,
      primaryLocation: primaryLocation ?? this.primaryLocation,
      availableEquipment: availableEquipment ?? this.availableEquipment,
      budgetForEquipment: budgetForEquipment ?? this.budgetForEquipment,
      scheduleFlexibility: scheduleFlexibility ?? this.scheduleFlexibility,
      healthConditions: healthConditions ?? this.healthConditions,
      physicalLimitations: physicalLimitations ?? this.physicalLimitations,
      exercisesToAvoid: exercisesToAvoid ?? this.exercisesToAvoid,
      additionalHealthNotes: additionalHealthNotes ?? this.additionalHealthNotes,
      motivationFactors: motivationFactors ?? this.motivationFactors,
      trackingPreference: trackingPreference ?? this.trackingPreference,
      wantsReminders: wantsReminders ?? this.wantsReminders,
      reminderTimes: reminderTimes ?? this.reminderTimes,
      wantsSocialFeatures: wantsSocialFeatures ?? this.wantsSocialFeatures,
      currentStep: currentStep ?? this.currentStep,
      isCompleted: isCompleted ?? this.isCompleted,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'age': age,
      'gender': gender?.name,
      'height': height,
      'heightUnit': heightUnit?.name,
      'weight': weight,
      'weightUnit': weightUnit?.name,
      'primaryGoals': primaryGoals.map((g) => g.name).toList(),
      'secondaryGoals': secondaryGoals.map((g) => g.name).toList(),
      'specificGoalDetails': specificGoalDetails,
      'goalTimeline': goalTimeline?.name,
      'overallExperience': overallExperience?.name,
      'cardioLevel': cardioLevel,
      'strengthLevel': strengthLevel,
      'flexibilityLevel': flexibilityLevel,
      'previousActivities': previousActivities,
      'currentWeeklyFrequency': currentWeeklyFrequency,
      'preferredWorkoutTypes': preferredWorkoutTypes.map((t) => t.name).toList(),
      'availableDays': availableDays.map((d) => d.index).toList(),
      'preferredWorkoutTime': preferredWorkoutTime != null 
          ? {'hour': preferredWorkoutTime!.hour, 'minute': preferredWorkoutTime!.minute}
          : null,
      'sessionDuration': sessionDuration,
      'weeklyFrequency': weeklyFrequency,
      'intensityPreference': intensityPreference?.name,
      'primaryLocation': primaryLocation?.name,
      'availableEquipment': availableEquipment.map((e) => e.name).toList(),
      'budgetForEquipment': budgetForEquipment,
      'scheduleFlexibility': scheduleFlexibility?.name,
      'healthConditions': healthConditions.map((h) => h.name).toList(),
      'physicalLimitations': physicalLimitations.map((p) => p.name).toList(),
      'exercisesToAvoid': exercisesToAvoid,
      'additionalHealthNotes': additionalHealthNotes,
      'motivationFactors': motivationFactors.map((m) => m.name).toList(),
      'trackingPreference': trackingPreference?.name,
      'wantsReminders': wantsReminders,
      'reminderTimes': reminderTimes.map((t) => {'hour': t.hour, 'minute': t.minute}).toList(),
      'wantsSocialFeatures': wantsSocialFeatures,
      'currentStep': currentStep,
      'isCompleted': isCompleted,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory EnhancedOnboardingData.fromJson(Map<String, dynamic> json) {
    return EnhancedOnboardingData(
      fullName: json['fullName'] as String?,
      age: json['age'] as int?,
      gender: json['gender'] != null ? Gender.values.byName(json['gender']) : null,
      height: json['height'] as double?,
      heightUnit: json['heightUnit'] != null ? HeightUnit.values.byName(json['heightUnit']) : null,
      weight: json['weight'] as double?,
      weightUnit: json['weightUnit'] != null ? WeightUnit.values.byName(json['weightUnit']) : null,
      primaryGoals: (json['primaryGoals'] as List<dynamic>?)
          ?.map((g) => FitnessGoal.values.byName(g))
          .toList() ?? [],
      secondaryGoals: (json['secondaryGoals'] as List<dynamic>?)
          ?.map((g) => FitnessGoal.values.byName(g))
          .toList() ?? [],
      specificGoalDetails: json['specificGoalDetails'] as String?,
      goalTimeline: json['goalTimeline'] != null ? GoalTimeline.values.byName(json['goalTimeline']) : null,
      overallExperience: json['overallExperience'] != null ? FitnessExperience.values.byName(json['overallExperience']) : null,
      cardioLevel: json['cardioLevel'] as int?,
      strengthLevel: json['strengthLevel'] as int?,
      flexibilityLevel: json['flexibilityLevel'] as int?,
      previousActivities: (json['previousActivities'] as List<dynamic>?)?.cast<String>() ?? [],
      currentWeeklyFrequency: json['currentWeeklyFrequency'] as int?,
      preferredWorkoutTypes: (json['preferredWorkoutTypes'] as List<dynamic>?)
          ?.map((t) => WorkoutType.values.byName(t))
          .toList() ?? [],
      availableDays: (json['availableDays'] as List<dynamic>?)
          ?.map((d) => DayOfWeek.values[d])
          .toList() ?? [],
      preferredWorkoutTime: json['preferredWorkoutTime'] != null
          ? TimeOfDay(
              hour: json['preferredWorkoutTime']['hour'],
              minute: json['preferredWorkoutTime']['minute'],
            )
          : null,
      sessionDuration: json['sessionDuration'] as int?,
      weeklyFrequency: json['weeklyFrequency'] as int?,
      intensityPreference: json['intensityPreference'] != null ? IntensityPreference.values.byName(json['intensityPreference']) : null,
      primaryLocation: json['primaryLocation'] != null ? WorkoutLocation.values.byName(json['primaryLocation']) : null,
      availableEquipment: (json['availableEquipment'] as List<dynamic>?)
          ?.map((e) => Equipment.values.byName(e))
          .toList() ?? [],
      budgetForEquipment: json['budgetForEquipment'] as double?,
      scheduleFlexibility: json['scheduleFlexibility'] != null ? ScheduleFlexibility.values.byName(json['scheduleFlexibility']) : null,
      healthConditions: (json['healthConditions'] as List<dynamic>?)
          ?.map((h) => HealthCondition.values.byName(h))
          .toList() ?? [],
      physicalLimitations: (json['physicalLimitations'] as List<dynamic>?)
          ?.map((p) => PhysicalLimitation.values.byName(p))
          .toList() ?? [],
      exercisesToAvoid: (json['exercisesToAvoid'] as List<dynamic>?)?.cast<String>() ?? [],
      additionalHealthNotes: json['additionalHealthNotes'] as String?,
      motivationFactors: (json['motivationFactors'] as List<dynamic>?)
          ?.map((m) => MotivationFactor.values.byName(m))
          .toList() ?? [],
      trackingPreference: json['trackingPreference'] != null ? TrackingPreference.values.byName(json['trackingPreference']) : null,
      wantsReminders: json['wantsReminders'] as bool? ?? false,
      reminderTimes: (json['reminderTimes'] as List<dynamic>?)
          ?.map((t) => TimeOfDay(hour: t['hour'], minute: t['minute']))
          .toList() ?? [],
      wantsSocialFeatures: json['wantsSocialFeatures'] as bool? ?? false,
      currentStep: json['currentStep'] as int? ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      lastUpdated: json['lastUpdated'] != null ? DateTime.parse(json['lastUpdated']) : null,
    );
  }

  /// Check if basic information is complete
  bool get hasBasicInfo => fullName != null && age != null && gender != null;

  /// Check if goals are defined
  bool get hasGoals => primaryGoals.isNotEmpty;

  /// Check if experience level is set
  bool get hasExperience => overallExperience != null;

  /// Check if preferences are set
  bool get hasPreferences => preferredWorkoutTypes.isNotEmpty && availableDays.isNotEmpty;

  /// Get completion percentage
  double get completionPercentage {
    int completedSections = 0;
    const int totalSections = 8;
    
    if (hasBasicInfo) completedSections++;
    if (hasGoals) completedSections++;
    if (hasExperience) completedSections++;
    if (hasPreferences) completedSections++;
    if (primaryLocation != null) completedSections++;
    if (healthConditions.isNotEmpty || physicalLimitations.isEmpty) completedSections++;
    if (motivationFactors.isNotEmpty) completedSections++;
    if (trackingPreference != null) completedSections++;
    
    return completedSections / totalSections;
  }

  @override
  List<Object?> get props => [
    fullName, age, gender, height, heightUnit, weight, weightUnit,
    primaryGoals, secondaryGoals, specificGoalDetails, goalTimeline,
    overallExperience, cardioLevel, strengthLevel, flexibilityLevel,
    previousActivities, currentWeeklyFrequency,
    preferredWorkoutTypes, availableDays, preferredWorkoutTime,
    sessionDuration, weeklyFrequency, intensityPreference,
    primaryLocation, availableEquipment, budgetForEquipment, scheduleFlexibility,
    healthConditions, physicalLimitations, exercisesToAvoid, additionalHealthNotes,
    motivationFactors, trackingPreference, wantsReminders, reminderTimes, wantsSocialFeatures,
    currentStep, isCompleted, lastUpdated,
  ];
}