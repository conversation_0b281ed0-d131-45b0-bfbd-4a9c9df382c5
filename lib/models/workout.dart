import 'package:openfit/models/exercise.dart';

class Workout {
  final String id;
  final String name;
  final String description;
  final String category;
  final String difficulty;
  final int estimatedDuration; // in minutes
  final List<Exercise> exercises;
  final String? imageUrl;
  final int caloriesBurn;

  Workout({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.estimatedDuration,
    required this.exercises,
    this.imageUrl,
    required this.caloriesBurn,
  });

  factory Workout.fromMap(Map<String, dynamic> map) => Workout(
        id: map['id'] ?? '',
        name: map['name'] ?? '',
        description: map['description'] ?? '',
        category: map['category'] ?? '',
        difficulty: map['difficulty'] ?? '',
        estimatedDuration: map['estimatedDuration'] ?? 0,
        exercises: (map['exercises'] as List<dynamic>?)
                ?.map((e) => Exercise.fromMap(e as Map<String, dynamic>))
                .toList() ??
            [],
        imageUrl: map['imageUrl'],
        caloriesBurn: map['caloriesBurn'] ?? 0,
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'description': description,
        'category': category,
        'difficulty': difficulty,
        'estimatedDuration': estimatedDuration,
        'exercises': exercises.map((e) => e.toMap()).toList(),
        'imageUrl': imageUrl,
        'caloriesBurn': caloriesBurn,
      };
}