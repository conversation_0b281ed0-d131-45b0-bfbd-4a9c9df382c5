enum Gender { male, female, other, preferNotToSay }

enum FitnessGoal {
  trainingForSport,
  increaseStrength,
  increaseStamina,
  loseWeight,
  buildMuscle,
  improveFlexibility,
  reduceStress,
  generalFitness,
  rehabilitation,
  athleticPerformance,
}

enum Equipment {
  dumbbells,
  barbell,
  kettlebells,
  resistanceBands,
  pullUpBar,
  benchPress,
  treadmill,
  elliptical,
  stationaryBike,
  yogaMat,
  foamRoller,
  medicineeBall,
  jumpRope,
  gymAccess,
  noEquipment,
}

enum HeightUnit { feet, cm }
enum WeightUnit { lbs, kg }
enum WorkoutDuration { minutes15, minutes30, minutes45, minutes60, minutes90, minutes120 }
enum WorkoutsPerWeek { one, two, three, four, five, six, seven }
enum DayOfWeek { monday, tuesday, wednesday, thursday, friday, saturday, sunday }

class OnboardingData {
  // Section 1: Tell Us About Yourself
  final String? fullName;
  final Gender? gender;
  final int? age;
  final double? height;
  final HeightUnit? heightUnit;
  final double? weight;
  final WeightUnit? weightUnit;
  
  // Section 2: Fitness Goals
  final List<FitnessGoal> fitnessGoals;
  final List<FitnessGoal> prioritizedGoals;
  final String? additionalGoalInfo;
  
  // Section 3: Current Fitness Levels
  final double? cardioLevel; // 0.0 to 4.0 (Low to Elite)
  final double? weightLiftingLevel; // 0.0 to 4.0 (Low to Elite)
  final String? additionalFitnessInfo;
  
  // Section 4: Equipment
  final List<Equipment> availableEquipment;
  
  // Section 5: Workout Schedule
  final WorkoutsPerWeek? workoutsPerWeek;
  final WorkoutDuration? workoutDuration;
  final bool? optimizeWorkoutTime;
  final List<DayOfWeek> preferredDays;
  final bool? optimizeWorkoutDays;
  
  // Section 6: Additional Information
  final String? additionalInfo;

  const OnboardingData({
    this.fullName,
    this.gender,
    this.age,
    this.height,
    this.heightUnit,
    this.weight,
    this.weightUnit,
    this.fitnessGoals = const [],
    this.prioritizedGoals = const [],
    this.additionalGoalInfo,
    this.cardioLevel,
    this.weightLiftingLevel,
    this.additionalFitnessInfo,
    this.availableEquipment = const [],
    this.workoutsPerWeek,
    this.workoutDuration,
    this.optimizeWorkoutTime,
    this.preferredDays = const [],
    this.optimizeWorkoutDays,
    this.additionalInfo,
  });

  OnboardingData copyWith({
    String? fullName,
    Gender? gender,
    int? age,
    double? height,
    HeightUnit? heightUnit,
    double? weight,
    WeightUnit? weightUnit,
    List<FitnessGoal>? fitnessGoals,
    List<FitnessGoal>? prioritizedGoals,
    String? additionalGoalInfo,
    double? cardioLevel,
    double? weightLiftingLevel,
    String? additionalFitnessInfo,
    List<Equipment>? availableEquipment,
    WorkoutsPerWeek? workoutsPerWeek,
    WorkoutDuration? workoutDuration,
    bool? optimizeWorkoutTime,
    List<DayOfWeek>? preferredDays,
    bool? optimizeWorkoutDays,
    String? additionalInfo,
  }) => OnboardingData(
        fullName: fullName ?? this.fullName,
        gender: gender ?? this.gender,
        age: age ?? this.age,
        height: height ?? this.height,
        heightUnit: heightUnit ?? this.heightUnit,
        weight: weight ?? this.weight,
        weightUnit: weightUnit ?? this.weightUnit,
        fitnessGoals: fitnessGoals ?? this.fitnessGoals,
        prioritizedGoals: prioritizedGoals ?? this.prioritizedGoals,
        additionalGoalInfo: additionalGoalInfo ?? this.additionalGoalInfo,
        cardioLevel: cardioLevel ?? this.cardioLevel,
        weightLiftingLevel: weightLiftingLevel ?? this.weightLiftingLevel,
        additionalFitnessInfo: additionalFitnessInfo ?? this.additionalFitnessInfo,
        availableEquipment: availableEquipment ?? this.availableEquipment,
        workoutsPerWeek: workoutsPerWeek ?? this.workoutsPerWeek,
        workoutDuration: workoutDuration ?? this.workoutDuration,
        optimizeWorkoutTime: optimizeWorkoutTime ?? this.optimizeWorkoutTime,
        preferredDays: preferredDays ?? this.preferredDays,
        optimizeWorkoutDays: optimizeWorkoutDays ?? this.optimizeWorkoutDays,
        additionalInfo: additionalInfo ?? this.additionalInfo,
      );

  bool get isBasicInfoComplete => fullName != null && gender != null && age != null;
  bool get isComplete => isBasicInfoComplete && fitnessGoals.isNotEmpty;
}

// Extensions for enum display names and descriptions
extension GenderExtension on Gender {
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.other:
        return 'Other';
      case Gender.preferNotToSay:
        return 'Prefer not to say';
    }
  }
}

extension FitnessGoalExtension on FitnessGoal {
  String get displayName {
    switch (this) {
      case FitnessGoal.trainingForSport:
        return 'Training for a specific sport';
      case FitnessGoal.increaseStrength:
        return 'Increase strength';
      case FitnessGoal.increaseStamina:
        return 'Increase stamina';
      case FitnessGoal.loseWeight:
        return 'Lose weight';
      case FitnessGoal.buildMuscle:
        return 'Build muscle';
      case FitnessGoal.improveFlexibility:
        return 'Improve flexibility';
      case FitnessGoal.reduceStress:
        return 'Reduce stress';
      case FitnessGoal.generalFitness:
        return 'General fitness';
      case FitnessGoal.rehabilitation:
        return 'Rehabilitation';
      case FitnessGoal.athleticPerformance:
        return 'Athletic performance';
    }
  }

  String get description {
    switch (this) {
      case FitnessGoal.trainingForSport:
        return 'Prepare for competitive sports';
      case FitnessGoal.increaseStrength:
        return 'Build muscle strength and power';
      case FitnessGoal.increaseStamina:
        return 'Improve endurance and stamina';
      case FitnessGoal.loseWeight:
        return 'Shed excess weight';
      case FitnessGoal.buildMuscle:
        return 'Increase muscle mass';
      case FitnessGoal.improveFlexibility:
        return 'Enhance flexibility and mobility';
      case FitnessGoal.reduceStress:
        return 'Relax and reduce stress';
      case FitnessGoal.generalFitness:
        return 'Maintain overall health';
      case FitnessGoal.rehabilitation:
        return 'Recover from injury';
      case FitnessGoal.athleticPerformance:
        return 'Enhance athletic performance';
    }
  }
}

extension EquipmentExtension on Equipment {
  String get displayName {
    switch (this) {
      case Equipment.dumbbells:
        return 'Dumbbells';
      case Equipment.barbell:
        return 'Barbell';
      case Equipment.kettlebells:
        return 'Kettlebells';
      case Equipment.resistanceBands:
        return 'Resistance Bands';
      case Equipment.pullUpBar:
        return 'Pull-up Bar';
      case Equipment.benchPress:
        return 'Bench Press';
      case Equipment.treadmill:
        return 'Treadmill';
      case Equipment.elliptical:
        return 'Elliptical';
      case Equipment.stationaryBike:
        return 'Stationary Bike';
      case Equipment.yogaMat:
        return 'Yoga Mat';
      case Equipment.foamRoller:
        return 'Foam Roller';
      case Equipment.medicineeBall:
        return 'Medicine Ball';
      case Equipment.jumpRope:
        return 'Jump Rope';
      case Equipment.gymAccess:
        return 'Gym Access';
      case Equipment.noEquipment:
        return 'No Equipment';
    }
  }
}

extension WorkoutsPerWeekExtension on WorkoutsPerWeek {
  String get displayName {
    switch (this) {
      case WorkoutsPerWeek.one:
        return '1 workout per week';
      case WorkoutsPerWeek.two:
        return '2 workouts per week';
      case WorkoutsPerWeek.three:
        return '3 workouts per week';
      case WorkoutsPerWeek.four:
        return '4 workouts per week';
      case WorkoutsPerWeek.five:
        return '5 workouts per week';
      case WorkoutsPerWeek.six:
        return '6 workouts per week';
      case WorkoutsPerWeek.seven:
        return '7 workouts per week';
    }
  }

  int get value {
    switch (this) {
      case WorkoutsPerWeek.one:
        return 1;
      case WorkoutsPerWeek.two:
        return 2;
      case WorkoutsPerWeek.three:
        return 3;
      case WorkoutsPerWeek.four:
        return 4;
      case WorkoutsPerWeek.five:
        return 5;
      case WorkoutsPerWeek.six:
        return 6;
      case WorkoutsPerWeek.seven:
        return 7;
    }
  }
}

extension WorkoutDurationExtension on WorkoutDuration {
  String get displayName {
    switch (this) {
      case WorkoutDuration.minutes15:
        return '15 minutes';
      case WorkoutDuration.minutes30:
        return '30 minutes';
      case WorkoutDuration.minutes45:
        return '45 minutes';
      case WorkoutDuration.minutes60:
        return '60 minutes';
      case WorkoutDuration.minutes90:
        return '90 minutes';
      case WorkoutDuration.minutes120:
        return '120 minutes';
    }
  }

  int get minutes {
    switch (this) {
      case WorkoutDuration.minutes15:
        return 15;
      case WorkoutDuration.minutes30:
        return 30;
      case WorkoutDuration.minutes45:
        return 45;
      case WorkoutDuration.minutes60:
        return 60;
      case WorkoutDuration.minutes90:
        return 90;
      case WorkoutDuration.minutes120:
        return 120;
    }
  }
}

extension DayOfWeekExtension on DayOfWeek {
  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Monday';
      case DayOfWeek.tuesday:
        return 'Tuesday';
      case DayOfWeek.wednesday:
        return 'Wednesday';
      case DayOfWeek.thursday:
        return 'Thursday';
      case DayOfWeek.friday:
        return 'Friday';
      case DayOfWeek.saturday:
        return 'Saturday';
      case DayOfWeek.sunday:
        return 'Sunday';
    }
  }

  String get shortName {
    switch (this) {
      case DayOfWeek.monday:
        return 'M';
      case DayOfWeek.tuesday:
        return 'T';
      case DayOfWeek.wednesday:
        return 'W';
      case DayOfWeek.thursday:
        return 'T';
      case DayOfWeek.friday:
        return 'F';
      case DayOfWeek.saturday:
        return 'S';
      case DayOfWeek.sunday:
        return 'S';
    }
  }
}

extension HeightUnitExtension on HeightUnit {
  String get displayName {
    switch (this) {
      case HeightUnit.feet:
        return 'ft/in';
      case HeightUnit.cm:
        return 'cm';
    }
  }
}

extension WeightUnitExtension on WeightUnit {
  String get displayName {
    switch (this) {
      case WeightUnit.lbs:
        return 'lbs';
      case WeightUnit.kg:
        return 'kg';
    }
  }
}