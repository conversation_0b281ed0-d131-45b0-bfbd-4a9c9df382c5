import 'package:equatable/equatable.dart';

/// Enhanced user profile model for Supabase integration
class UserProfile extends Equatable {
  final String id;
  final String? email;
  final String? fullName;
  final String? avatarUrl;
  final int? age;
  final String? gender;
  final int? heightCm;
  final double? weightKg;
  final String? fitnessLevel;
  final List<String> goals;
  final List<String> preferredWorkoutDays;
  final String? preferredWorkoutTime;
  final bool onboardingCompleted;
  final bool hasCompletedPreferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserProfile({
    required this.id,
    this.email,
    this.fullName,
    this.avatarUrl,
    this.age,
    this.gender,
    this.heightCm,
    this.weightKg,
    this.fitnessLevel,
    this.goals = const [],
    this.preferredWorkoutDays = const [],
    this.preferredWorkoutTime,
    this.onboardingCompleted = false,
    this.hasCompletedPreferences = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create UserProfile from Supabase JSON (matching your existing schema)
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    // Handle fitness goals array - extract names from objects
    List<String> parseGoals(dynamic goalsData) {
      if (goalsData == null) return [];
      if (goalsData is List) {
        return goalsData.map((goal) {
          if (goal is Map<String, dynamic> && goal.containsKey('name')) {
            return goal['name'] as String;
          } else if (goal is String) {
            return goal;
          }
          return goal.toString();
        }).toList();
      }
      return [];
    }

    // Handle height conversion - database might store in different units
    int? parseHeight(dynamic height, String? unit) {
      if (height == null) return null;
      final heightValue = (height as num).toDouble();
      
      // Convert to cm if needed
      if (unit == 'ft' || unit == 'feet') {
        return (heightValue * 30.48).round(); // feet to cm
      } else if (unit == 'in' || unit == 'inches') {
        return (heightValue * 2.54).round(); // inches to cm
      }
      
      // Assume cm if no unit or unit is cm
      return heightValue.round();
    }

    // Handle weight conversion
    double? parseWeight(dynamic weight, String? unit) {
      if (weight == null) return null;
      final weightValue = (weight as num).toDouble();
      
      // Convert to kg if needed
      if (unit == 'lbs' || unit == 'lb') {
        return weightValue * 0.453592; // lbs to kg
      }
      
      // Assume kg if no unit or unit is kg
      return weightValue;
    }

    return UserProfile(
      id: json['id'] as String,
      email: json['email'] as String?,
      fullName: json['display_name'] as String?,
      avatarUrl: null,
      age: json['age'] as int?,
      gender: json['gender'] as String?,
      heightCm: parseHeight(json['height'], json['height_unit'] as String?),
      weightKg: parseWeight(json['weight'], json['weight_unit'] as String?),
      fitnessLevel: json['fitness_experience'] as String?,
      goals: parseGoals(json['fitness_goals_array']),
      preferredWorkoutDays: json['workout_days'] != null 
          ? List<String>.from(json['workout_days']) : [],
      preferredWorkoutTime: json['preferred_workout_duration'] as String?,
      onboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      hasCompletedPreferences: json['has_completed_preferences'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String? ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] as String? ?? DateTime.now().toIso8601String()),
    );
  }

  /// Convert UserProfile to JSON for Supabase (matching your existing schema)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'display_name': fullName, // Your schema uses display_name
      'age': age,
      'gender': gender,
      'height': heightCm,
      'weight': weightKg,
      'fitness_experience': fitnessLevel, // Your schema uses fitness_experience
      'fitness_goals_array': goals,
      'workout_days': preferredWorkoutDays,
      'preferred_workout_duration': preferredWorkoutTime,
      'onboarding_completed': onboardingCompleted,
      'has_completed_preferences': hasCompletedPreferences,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  UserProfile copyWith({
    String? id,
    String? email,
    String? fullName,
    String? avatarUrl,
    int? age,
    String? gender,
    int? heightCm,
    double? weightKg,
    String? fitnessLevel,
    List<String>? goals,
    List<String>? preferredWorkoutDays,
    String? preferredWorkoutTime,
    bool? onboardingCompleted,
    bool? hasCompletedPreferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      heightCm: heightCm ?? this.heightCm,
      weightKg: weightKg ?? this.weightKg,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
      goals: goals ?? this.goals,
      preferredWorkoutDays: preferredWorkoutDays ?? this.preferredWorkoutDays,
      preferredWorkoutTime: preferredWorkoutTime ?? this.preferredWorkoutTime,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      hasCompletedPreferences: hasCompletedPreferences ?? this.hasCompletedPreferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get BMI if height and weight are available
  double? get bmi {
    if (heightCm != null && weightKg != null && heightCm! > 0) {
      final heightM = heightCm! / 100.0;
      return weightKg! / (heightM * heightM);
    }
    return null;
  }

  /// Get BMI category
  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  /// Check if profile is complete enough for workouts
  bool get isProfileComplete {
    return fullName != null && 
           age != null && 
           gender != null && 
           fitnessLevel != null &&
           goals.isNotEmpty;
  }

  @override
  List<Object?> get props => [
    id, email, fullName, avatarUrl, age, gender, heightCm, weightKg,
    fitnessLevel, goals, preferredWorkoutDays, preferredWorkoutTime,
    onboardingCompleted, hasCompletedPreferences, createdAt, updatedAt,
  ];
}