/// Supabase Exercise model matching the database schema
class SupabaseExercise {
  final String id;
  final String name;
  final String? description;
  final String? videoUrl;
  final String? primaryMuscle;
  final String? secondaryMuscle;
  final String? equipment;
  final String? instructions;
  final String? category;
  final String? verticalVideo;
  final DateTime createdAt;

  const SupabaseExercise({
    required this.id,
    required this.name,
    this.description,
    this.videoUrl,
    this.primaryMuscle,
    this.secondaryMuscle,
    this.equipment,
    this.instructions,
    this.category,
    this.verticalVideo,
    required this.createdAt,
  });

  factory SupabaseExercise.fromJson(Map<String, dynamic> json) {
    return SupabaseExercise(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      videoUrl: json['video_url'] as String?,
      primaryMuscle: json['primary_muscle'] as String?,
      secondaryMuscle: json['secondary_muscle'] as String?,
      equipment: json['equipment'] as String?,
      instructions: json['instructions'] as String?,
      category: json['category'] as String?,
      verticalVideo: json['vertical_video'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'video_url': videoUrl,
      'primary_muscle': primaryMuscle,
      'secondary_muscle': secondaryMuscle,
      'equipment': equipment,
      'instructions': instructions,
      'category': category,
      'vertical_video': verticalVideo,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Convert to legacy Exercise model for compatibility
  Exercise toLegacyExercise() {
    return Exercise(
      id: id,
      name: name,
      description: description ?? '',
      category: category ?? 'General',
      difficulty: _getDifficultyFromEquipment(),
      duration: 60, // Default duration
      restTime: 30, // Default rest time
      imageUrl: null,
      instructions: _parseInstructions(),
      muscleGroups: _combineMuscleGroups(),
    );
  }

  String _getDifficultyFromEquipment() {
    if (equipment == null || equipment == 'None') return 'Beginner';
    if (equipment!.contains('Barbell') || equipment!.contains('Heavy')) {
      return 'Advanced';
    }
    return 'Intermediate';
  }

  List<String> _parseInstructions() {
    if (instructions == null) return [];
    return instructions!
        .split(RegExp(r'\d+\.'))
        .where((step) => step.trim().isNotEmpty)
        .map((step) => step.trim())
        .toList();
  }

  String _combineMuscleGroups() {
    final muscles = <String>[];
    if (primaryMuscle != null) muscles.add(primaryMuscle!);
    if (secondaryMuscle != null) muscles.add(secondaryMuscle!);
    return muscles.join(', ');
  }
}

/// Legacy Exercise model for backward compatibility
class Exercise {
  final String id;
  final String name;
  final String description;
  final String category;
  final String difficulty;
  final int duration;
  final int restTime;
  final String? imageUrl;
  final List<String> instructions;
  final String muscleGroups;

  const Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.duration,
    required this.restTime,
    this.imageUrl,
    required this.instructions,
    required this.muscleGroups,
  });

  factory Exercise.fromMap(Map<String, dynamic> map) => Exercise(
        id: map['id'] ?? '',
        name: map['name'] ?? '',
        description: map['description'] ?? '',
        category: map['category'] ?? '',
        difficulty: map['difficulty'] ?? '',
        duration: map['duration'] ?? 0,
        restTime: map['restTime'] ?? 0,
        imageUrl: map['imageUrl'],
        instructions: List<String>.from(map['instructions'] ?? []),
        muscleGroups: map['muscleGroups'] ?? '',
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'description': description,
        'category': category,
        'difficulty': difficulty,
        'duration': duration,
        'restTime': restTime,
        'imageUrl': imageUrl,
        'instructions': instructions,
        'muscleGroups': muscleGroups,
      };
}