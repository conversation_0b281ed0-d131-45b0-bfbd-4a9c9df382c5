class Exercise {
  final String id;
  final String name;
  final String description;
  final String category;
  final String difficulty;
  final int duration; // in seconds
  final int restTime; // in seconds
  final String? imageUrl;
  final List<String> instructions;
  final String muscleGroups;

  Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.duration,
    required this.restTime,
    this.imageUrl,
    required this.instructions,
    required this.muscleGroups,
  });

  factory Exercise.fromMap(Map<String, dynamic> map) => Exercise(
        id: map['id'] ?? '',
        name: map['name'] ?? '',
        description: map['description'] ?? '',
        category: map['category'] ?? '',
        difficulty: map['difficulty'] ?? '',
        duration: map['duration'] ?? 0,
        restTime: map['restTime'] ?? 0,
        imageUrl: map['imageUrl'],
        instructions: List<String>.from(map['instructions'] ?? []),
        muscleGroups: map['muscleGroups'] ?? '',
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'description': description,
        'category': category,
        'difficulty': difficulty,
        'duration': duration,
        'restTime': restTime,
        'imageUrl': imageUrl,
        'instructions': instructions,
        'muscleGroups': muscleGroups,
      };
}