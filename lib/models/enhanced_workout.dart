import 'package:equatable/equatable.dart';

/// Enhanced workout model for Supabase integration
class EnhancedWorkout extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String category;
  final String difficulty;
  final int estimatedDuration; // in minutes
  final List<String> equipmentNeeded;
  final List<String> targetMuscles;
  final String? imageUrl;
  final bool isPublic;
  final String? createdBy;
  final List<WorkoutExercise> exercises;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EnhancedWorkout({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.difficulty,
    required this.estimatedDuration,
    this.equipmentNeeded = const [],
    this.targetMuscles = const [],
    this.imageUrl,
    this.isPublic = true,
    this.createdBy,
    this.exercises = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory EnhancedWorkout.fromJson(Map<String, dynamic> json) {
    return EnhancedWorkout(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: json['category'] as String,
      difficulty: json['difficulty'] as String,
      estimatedDuration: json['estimated_duration'] as int,
      equipmentNeeded: json['equipment_needed'] != null 
          ? List<String>.from(json['equipment_needed']) : [],
      targetMuscles: json['target_muscles'] != null 
          ? List<String>.from(json['target_muscles']) : [],
      imageUrl: json['image_url'] as String?,
      isPublic: json['is_public'] as bool? ?? true,
      createdBy: json['created_by'] as String?,
      exercises: json['workout_exercises'] != null
          ? (json['workout_exercises'] as List)
              .map((e) => WorkoutExercise.fromJson(e))
              .toList()
          : [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'difficulty': difficulty,
      'estimated_duration': estimatedDuration,
      'equipment_needed': equipmentNeeded,
      'target_muscles': targetMuscles,
      'image_url': imageUrl,
      'is_public': isPublic,
      'created_by': createdBy,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EnhancedWorkout copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? difficulty,
    int? estimatedDuration,
    List<String>? equipmentNeeded,
    List<String>? targetMuscles,
    String? imageUrl,
    bool? isPublic,
    String? createdBy,
    List<WorkoutExercise>? exercises,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EnhancedWorkout(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      equipmentNeeded: equipmentNeeded ?? this.equipmentNeeded,
      targetMuscles: targetMuscles ?? this.targetMuscles,
      imageUrl: imageUrl ?? this.imageUrl,
      isPublic: isPublic ?? this.isPublic,
      createdBy: createdBy ?? this.createdBy,
      exercises: exercises ?? this.exercises,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get total number of exercises
  int get totalExercises => exercises.length;

  /// Get total estimated calories burned (rough estimate)
  int get estimatedCalories {
    // Rough calculation: 5-10 calories per minute based on difficulty
    final multiplier = difficulty == 'beginner' ? 5 : 
                      difficulty == 'intermediate' ? 7 : 10;
    return estimatedDuration * multiplier;
  }

  @override
  List<Object?> get props => [
    id, name, description, category, difficulty, estimatedDuration,
    equipmentNeeded, targetMuscles, imageUrl, isPublic, createdBy,
    exercises, createdAt, updatedAt,
  ];
}

/// Workout exercise junction model
class WorkoutExercise extends Equatable {
  final String id;
  final String workoutId;
  final String exerciseId;
  final int orderIndex;
  final int? sets;
  final int? reps;
  final int? durationSeconds;
  final int? restSeconds;
  final double? weightKg;
  final String? notes;
  final EnhancedExercise? exercise; // Populated when joined

  const WorkoutExercise({
    required this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.orderIndex,
    this.sets,
    this.reps,
    this.durationSeconds,
    this.restSeconds,
    this.weightKg,
    this.notes,
    this.exercise,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      id: json['id'] as String,
      workoutId: json['workout_id'] as String,
      exerciseId: json['exercise_id'] as String,
      orderIndex: json['order_index'] as int,
      sets: json['sets'] as int?,
      reps: json['reps'] as int?,
      durationSeconds: json['duration_seconds'] as int?,
      restSeconds: json['rest_seconds'] as int?,
      weightKg: json['weight_kg'] != null ? (json['weight_kg'] as num).toDouble() : null,
      notes: json['notes'] as String?,
      exercise: json['exercise'] != null 
          ? EnhancedExercise.fromJson(json['exercise']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'order_index': orderIndex,
      'sets': sets,
      'reps': reps,
      'duration_seconds': durationSeconds,
      'rest_seconds': restSeconds,
      'weight_kg': weightKg,
      'notes': notes,
    };
  }

  @override
  List<Object?> get props => [
    id, workoutId, exerciseId, orderIndex, sets, reps,
    durationSeconds, restSeconds, weightKg, notes, exercise,
  ];
}

/// Enhanced exercise model
class EnhancedExercise extends Equatable {
  final String id;
  final String name;
  final String? description;
  final List<String> instructions;
  final String category;
  final String? equipment;
  final String? primaryMuscle;
  final List<String> secondaryMuscles;
  final String difficulty;
  final String? imageUrl;
  final String? videoUrl;
  final int? durationSeconds;
  final String? repsRange;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EnhancedExercise({
    required this.id,
    required this.name,
    this.description,
    this.instructions = const [],
    required this.category,
    this.equipment,
    this.primaryMuscle,
    this.secondaryMuscles = const [],
    required this.difficulty,
    this.imageUrl,
    this.videoUrl,
    this.durationSeconds,
    this.repsRange,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EnhancedExercise.fromJson(Map<String, dynamic> json) {
    return EnhancedExercise(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      instructions: json['instructions'] != null 
          ? List<String>.from(json['instructions']) : [],
      category: json['category'] as String,
      equipment: json['equipment'] as String?,
      primaryMuscle: json['primary_muscle'] as String?,
      secondaryMuscles: json['secondary_muscles'] != null 
          ? List<String>.from(json['secondary_muscles']) : [],
      difficulty: json['difficulty'] as String,
      imageUrl: json['image_url'] as String?,
      videoUrl: json['video_url'] as String?,
      durationSeconds: json['duration_seconds'] as int?,
      repsRange: json['reps_range'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'instructions': instructions,
      'category': category,
      'equipment': equipment,
      'primary_muscle': primaryMuscle,
      'secondary_muscles': secondaryMuscles,
      'difficulty': difficulty,
      'image_url': imageUrl,
      'video_url': videoUrl,
      'duration_seconds': durationSeconds,
      'reps_range': repsRange,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
    id, name, description, instructions, category, equipment,
    primaryMuscle, secondaryMuscles, difficulty, imageUrl, videoUrl,
    durationSeconds, repsRange, createdAt, updatedAt,
  ];
}