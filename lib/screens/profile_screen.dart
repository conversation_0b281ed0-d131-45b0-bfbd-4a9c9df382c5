import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/data/sample_data.dart';
import 'package:openfit/providers/enhanced_auth_providers.dart';
import 'package:openfit/config/supabase_config.dart';
import 'package:openfit/services/supabase_service_simple.dart';
import 'package:openfit/screens/workout_timer_screen.dart';
import 'package:openfit/screens/onboarding/onboarding_flow.dart';
import 'package:openfit/screens/onboarding_components_test.dart';
import 'package:openfit/screens/modern_components_demo.dart';
import 'package:openfit/screens/progress_tracking_demo.dart';
import 'package:openfit/widgets/top_knob_bar.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTestOnboarding() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.science_rounded,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text('Test Onboarding'),
            ],
          ),
          content: const Text(
            'This will reset your onboarding status and take you through the onboarding flow again.\n\n'
            'Your profile data will be preserved and pre-populated in the forms.\n\n'
            'This is a development-only feature for testing purposes.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(); // Close dialog
                
                // Show loading snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Row(
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 12),
                        Text('Resetting onboarding...'),
                      ],
                    ),
                    duration: Duration(seconds: 2),
                  ),
                );
                
                try {
                  // Direct database update for testing
                  final supabaseService = ref.read(supabaseServiceProvider);
                  await supabaseService.upsertUserProfile({
                    'onboarding_completed': false,
                    'has_completed_preferences': false,
                  });
                  
                  // Force refresh both profile providers to trigger AuthWrapper re-evaluation
                  ref.invalidate(userProfileProvider);
                  ref.invalidate(profileNotifierProvider);
                  
                  // Add a small delay to ensure the database update propagates
                  await Future.delayed(const Duration(milliseconds: 500));
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('✅ Onboarding reset! Redirecting to onboarding flow...'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                  
                  // Debug: Check the profile state after reset
                  final profile = await ref.read(userProfileProvider.future);
                  print('Profile after reset: onboardingCompleted = ${profile?.onboardingCompleted}');
                  
                  // If AuthWrapper doesn't automatically redirect, do it manually
                  if (profile != null && !profile.onboardingCompleted && mounted) {
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (context) => const OnboardingFlow()),
                      (route) => false,
                    );
                  }
                  
                  // AuthWrapper will automatically detect onboarding_completed = false
                  // and navigate to OnboardingFlow
                } catch (error) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('❌ Failed to reset onboarding: ${error.toString()}'),
                        backgroundColor: Colors.red,
                        action: SnackBarAction(
                          label: 'Retry',
                          onPressed: _handleTestOnboarding,
                        ),
                      ),
                    );
                  }
                }
              },
              child: const Text('Reset & Test'),
            ),
          ],
        );
      },
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(); // Close dialog
                
                try {
                  await ref.read(authNotifierProvider.notifier).signOut();
                  // AuthWrapper will automatically handle navigation to sign in screen
                } catch (error) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to sign out: ${error.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('Sign Out'),
            ),
          ],
        );
      },
    );
  }

  void _handleTasteWorkoutScreen() {
    // Use the first workout for the preview
    final previewWorkout = SampleData.workouts.first;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutTimerScreen(
          workout: previewWorkout,
          isPreviewMode: true,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Stack(
          children: [
            // Background gradient blobs
            Positioned(
              right: -100,
              top: 200,
              child: Container(
                width: 300,
                height: 600,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF8FBC8F).withValues(alpha: 0.3),
                      const Color(0xFF6B8E6B).withValues(alpha: 0.2),
                      const Color(0xFF8FBC8F).withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(150),
                ),
              ),
            ),
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    children: [
                      // Top Knob Bar
                      const TopKnobBar(
                        title: 'My Account',
                        showGrid: false,
                      ),
                      const SizedBox(height: 24),
                      
                      // Profile Header Section
                      _buildProfileHeader(),
                      const SizedBox(height: 24),
                      
                      // Quick Stats Section
                      _buildQuickStats(),
                      const SizedBox(height: 32),
                      
                      // Menu Items
                      _buildMenuList(),
                      
                      const SizedBox(height: 100), // Space for bottom navigation
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        // Movemate-style gradient background
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF4ADE80), // Green accent
            Color(0xFF22C55E), // Darker green
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4ADE80).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Avatar with Edit Button - Movemate Style
          Stack(
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: Image.network(
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(18),
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                top: -4,
                right: -4,
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B47),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.edit,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // User Name and Email - Movemate Style
          Consumer(
            builder: (context, ref, child) {
              final user = ref.watch(currentUserProvider);
              final userProfile = ref.watch(userProfileProvider);
              
              return Column(
                children: [
                  Text(
                    userProfile.when(
                      data: (profile) => profile?.fullName ?? user?.userMetadata?['display_name'] ?? 'Adam Smith',
                      loading: () => 'Loading...',
                      error: (_, __) => user?.userMetadata?['display_name'] ?? 'Adam Smith',
                    ),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user?.email ?? '<EMAIL>',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 4),
          
          // Phone
          Text(
            '***** 0245 8921',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),
          
          // Personal Stats - Movemate Style
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatColumn('Gender', 'Male'),
              _buildStatColumn('Age', '26'),
              _buildStatColumn('Height', '175 cm'),
              _buildStatColumn('Weight', '73 kg'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatColumn(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuList() {
    final theme = Theme.of(context);
    
    final menuItems = [
      _MenuItemData(
        icon: Icons.person_outline_rounded,
        title: 'My Account',
        subtitle: '',
        onTap: () {},
        isHighlighted: true,
      ),
      _MenuItemData(
        icon: Icons.track_changes_rounded,
        title: 'My Goals',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.history_rounded,
        title: 'Workout History',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.analytics_outlined,
        title: 'Activity Tracking',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.restaurant_menu_rounded,
        title: 'Nutrition Tracking',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.bookmark_outline_rounded,
        title: 'Subscription',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.star_outline_rounded,
        title: 'Favorites',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.play_circle_outline_rounded,
        title: 'Tutorials',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.preview_rounded,
        title: 'Taste Workout Screen',
        subtitle: '',
        onTap: _handleTasteWorkoutScreen,
      ),
      _MenuItemData(
        icon: Icons.palette_outlined,
        title: 'Theme',
        subtitle: '',
        onTap: () {},
      ),
      _MenuItemData(
        icon: Icons.help_outline_rounded,
        title: 'Help & Support',
        subtitle: '',
        onTap: () {},
      ),
      // Development only - Test Onboarding
      if (SupabaseConfig.isDevelopment)
        _MenuItemData(
          icon: Icons.science_rounded, // Debug/test icon
          title: 'Test Onboarding (Dev Only)',
          subtitle: '',
          onTap: _handleTestOnboarding,
          isDestructive: false,
        ),
      // Development only - Test Components
      if (SupabaseConfig.isDevelopment)
        _MenuItemData(
          icon: Icons.widgets_rounded, // Components test icon
          title: 'Test Components (Dev Only)',
          subtitle: '',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OnboardingComponentsTestScreen(),
              ),
            );
          },
          isDestructive: false,
        ),
      // Development only - Modern Components Demo
      if (SupabaseConfig.isDevelopment)
        _MenuItemData(
          icon: Icons.design_services_rounded, // Modern design icon
          title: 'Modern Components Demo (Dev Only)',
          subtitle: '',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ModernComponentsDemo(),
              ),
            );
          },
          isDestructive: false,
        ),
      // Development only - Progress Tracking Demo
      if (SupabaseConfig.isDevelopment)
        _MenuItemData(
          icon: Icons.timeline_rounded, // Progress tracking icon
          title: 'Progress Tracking Demo (Dev Only)',
          subtitle: '',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ProgressTrackingDemo(),
              ),
            );
          },
          isDestructive: false,
        ),
      _MenuItemData(
        icon: Icons.logout_rounded,
        title: 'Log Out',
        subtitle: '',
        onTap: _handleLogout,
        isDestructive: true,
      ),
    ];

    return Column(
      children: [
        for (int i = 0; i < menuItems.length; i++) ...[
          _SimpleMenuItem(
            icon: menuItems[i].icon,
            title: menuItems[i].title,
            onTap: menuItems[i].onTap,
            isDestructive: menuItems[i].isDestructive,
            isHighlighted: menuItems[i].isHighlighted,
          ),
          if (i < menuItems.length - 1)
            const SizedBox(height: 4),
        ],
      ],
    );
  }
  
  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2D3A), // Movemate card background
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Consistent Streaks',
                        style: TextStyle(
                          color: const Color(0xFF9CA3AF),
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFF6B47),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const Icon(
                              Icons.local_fire_department,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            '21',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2D3A), // Movemate card background
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This Week Tasks',
                  style: TextStyle(
                    color: const Color(0xFF9CA3AF),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '05',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFF6B47),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
}

class _MenuItemData {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final bool isDestructive;
  final bool isHighlighted;

  _MenuItemData({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.isDestructive = false,
    this.isHighlighted = false,
  });
}

class _SimpleMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isDestructive;
  final bool isHighlighted;

  const _SimpleMenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isDestructive = false,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        decoration: BoxDecoration(
          color: isHighlighted 
              ? const Color(0xFFFF6B47) 
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isHighlighted 
                    ? Colors.white.withOpacity(0.2)
                    : const Color(0xFF2A2D3A),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: isHighlighted 
                    ? Colors.white
                    : isDestructive
                        ? const Color(0xFFEF4444)
                        : const Color(0xFF9CA3AF),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isHighlighted 
                      ? Colors.white
                      : isDestructive
                          ? const Color(0xFFEF4444)
                          : Colors.white,
                  fontSize: 16,
                  fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),
            if (!isDestructive)
              Icon(
                Icons.arrow_forward_ios,
                color: isHighlighted 
                    ? Colors.white.withOpacity(0.7)
                    : const Color(0xFF9CA3AF),
                size: 16,
              ),
          ],
        ),
      ),
    );
  }
}

