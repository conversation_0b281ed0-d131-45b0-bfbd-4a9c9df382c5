import 'package:flutter/material.dart';
import '../widgets/onboarding/modern_choice_card.dart';
import '../widgets/onboarding/modern_slider.dart';
import '../widgets/onboarding/modern_text_input.dart';
import '../widgets/onboarding/modern_multi_select.dart';
import '../widgets/onboarding/modern_rating.dart';

class OnboardingComponentsTestScreen extends StatefulWidget {
  const OnboardingComponentsTestScreen({super.key});

  @override
  State<OnboardingComponentsTestScreen> createState() => _OnboardingComponentsTestScreenState();
}

class _OnboardingComponentsTestScreenState extends State<OnboardingComponentsTestScreen> {
  String selectedChoice = '';
  double sliderValue = 50.0;
  String textValue = '';
  List<String> selectedMultiple = [];
  int ratingValue = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D2B),
      appBar: AppBar(
        title: const Text('Onboarding Components Test'),
        backgroundColor: const Color(0xFF2A2D3A),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ModernChoiceCard Test
            const Text(
              'ModernChoiceCard',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ModernChoiceCard(
              title: 'Weight Loss',
              subtitle: 'Burn calories and lose weight',
              icon: Icons.fitness_center,
              isSelected: selectedChoice == 'weight_loss',
              onTap: () {
                setState(() {
                  selectedChoice = selectedChoice == 'weight_loss' ? '' : 'weight_loss';
                });
              },
            ),
            ModernChoiceCard(
              title: 'Muscle Gain',
              subtitle: 'Build strength and muscle mass',
              icon: Icons.sports_gymnastics,
              isSelected: selectedChoice == 'muscle_gain',
              onTap: () {
                setState(() {
                  selectedChoice = selectedChoice == 'muscle_gain' ? '' : 'muscle_gain';
                });
              },
            ),
            
            const SizedBox(height: 32),
            
            // ModernSlider Test
            const Text(
              'ModernSlider',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ModernSlider(
              label: 'Age',
              value: sliderValue,
              min: 18,
              max: 80,
              unit: ' years',
              onChanged: (value) {
                setState(() {
                  sliderValue = value;
                });
              },
            ),
            
            const SizedBox(height: 32),
            
            // ModernTextInput Test
            const Text(
              'ModernTextInput',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ModernTextInput(
              label: 'Full Name',
              hint: 'Enter your full name',
              onChanged: (value) {
                setState(() {
                  textValue = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 32),
            
            // ModernMultiSelect Test
            const Text(
              'ModernMultiSelect',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ModernMultiSelect<String>(
              title: 'Fitness Goals',
              subtitle: 'Select your primary fitness goals',
              options: const [
                ModernMultiSelectOption(
                  value: 'weight_loss',
                  title: 'Weight Loss',
                  icon: Icons.trending_down,
                ),
                ModernMultiSelectOption(
                  value: 'muscle_gain',
                  title: 'Muscle Gain',
                  icon: Icons.fitness_center,
                ),
                ModernMultiSelectOption(
                  value: 'endurance',
                  title: 'Endurance',
                  icon: Icons.directions_run,
                ),
                ModernMultiSelectOption(
                  value: 'flexibility',
                  title: 'Flexibility',
                  icon: Icons.self_improvement,
                ),
              ],
              selectedValues: selectedMultiple,
              onSelectionChanged: (values) {
                setState(() {
                  selectedMultiple = values;
                });
              },
              maxSelections: 3,
            ),
            
            const SizedBox(height: 32),
            
            // ModernRating Test
            const Text(
              'ModernRating',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ModernRating(
              label: 'Current Fitness Level',
              description: 'Rate your current fitness level',
              value: ratingValue,
              maxRating: 5,
              type: RatingType.stars,
              onRatingChanged: (value) {
                setState(() {
                  ratingValue = value;
                });
              },
              scaleLabels: const [
                'Beginner',
                'Novice',
                'Intermediate',
                'Advanced',
                'Expert',
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Values Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2A2D3A),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Current Values:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Choice: $selectedChoice', style: const TextStyle(color: Colors.white)),
                  Text('Slider: ${sliderValue.round()}', style: const TextStyle(color: Colors.white)),
                  Text('Text: $textValue', style: const TextStyle(color: Colors.white)),
                  Text('Multi-select: ${selectedMultiple.join(', ')}', style: const TextStyle(color: Colors.white)),
                  Text('Rating: $ratingValue', style: const TextStyle(color: Colors.white)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}