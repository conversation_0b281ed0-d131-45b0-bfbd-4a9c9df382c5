import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/providers/app_state.dart';
import 'package:openfit/screens/diary_screen.dart';
import 'package:openfit/screens/home_screen.dart';
import 'package:openfit/screens/profile_screen.dart';
import 'package:openfit/screens/workouts_screen.dart';
import 'package:openfit/widgets/modern_bottom_nav.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(navigationProvider);
    
    final screens = [
      const HomeScreen(),
      const WorkoutsScreen(),
      const DiaryScreen(),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: screens[selectedIndex],
      extendBody: true,
      bottomNavigationBar: Container(
        margin: const EdgeInsets.all(20),
        child: ModernBottomNav(
          currentIndex: selectedIndex,
          onTap: (index) => ref.read(navigationProvider.notifier).setIndex(index),
        ),
      ),
    );
  }


}
