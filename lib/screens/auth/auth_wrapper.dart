import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/providers/enhanced_auth_providers.dart';
import 'package:openfit/screens/auth/sign_in_screen.dart';
import 'package:openfit/screens/main_screen.dart';
import 'package:openfit/screens/onboarding/onboarding_flow.dart';
import 'package:openfit/widgets/error_display.dart';

/// Authentication wrapper that determines which screen to show
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final currentUser = ref.watch(currentUserProvider);

    return authState.when(
      data: (state) {
        final user = state.session?.user;
        
        if (user == null) {
          // User not authenticated - show sign in
          return const SignInScreen();
        }
        
        // User is authenticated - check profile and onboarding status
        final userProfile = ref.watch(userProfileProvider);
        return userProfile.when(
          data: (profile) {
            if (profile == null || !profile.onboardingCompleted) {
              // User needs to complete onboarding
              return const OnboardingFlow();
            }
            // User is fully set up - show main app
            return const MainScreen();
          },
          loading: () => const LoadingDisplay(message: 'Loading your profile...'),
          error: (error, _) => ErrorDisplay(
            error: error,
            onRetry: () => ref.refresh(userProfileProvider),
            message: 'Failed to load your profile',
          ),
        );
      },
      loading: () => const LoadingDisplay(message: 'Checking authentication...'),
      error: (error, _) => ErrorDisplay(
        error: error,
        onRetry: () => ref.refresh(authStateProvider),
        message: 'Authentication error',
      ),
    );
  }
}