import 'package:flutter/material.dart';
import '../widgets/onboarding/modern_widgets.dart';

class ModernComponentsDemo extends StatefulWidget {
  const ModernComponentsDemo({super.key});

  @override
  State<ModernComponentsDemo> createState() => _ModernComponentsDemoState();
}

class _ModernComponentsDemoState extends State<ModernComponentsDemo> {
  // Choice card state
  String selectedChoice = '';
  
  // Slider state
  double sliderValue = 50.0;
  
  // Text input state
  String textValue = '';
  
  // Multi-select state
  List<String> selectedOptions = [];
  
  // Rating state
  int starRating = 0;
  int scaleRating = 0;
  int emojiRating = 0;
  int thumbsRating = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D2B),
      appBar: AppBar(
        title: const Text('Modern Components Demo'),
        backgroundColor: const Color(0xFF2A2D3A),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Choice Cards
            _buildSection(
              'Modern Choice Cards',
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ModernChoiceCard(
                          title: 'Beginner',
                          subtitle: 'Just starting out',
                          icon: Icons.star_border,
                          isSelected: selectedChoice == 'beginner',
                          onTap: () => setState(() => selectedChoice = 'beginner'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ModernChoiceCard(
                          title: 'Intermediate',
                          subtitle: 'Some experience',
                          icon: Icons.star_half,
                          isSelected: selectedChoice == 'intermediate',
                          onTap: () => setState(() => selectedChoice = 'intermediate'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ModernChoiceCard(
                    title: 'Advanced',
                    subtitle: 'Experienced athlete',
                    icon: Icons.star,
                    isSelected: selectedChoice == 'advanced',
                    onTap: () => setState(() => selectedChoice = 'advanced'),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Slider
            _buildSection(
              'Modern Slider',
              ModernSlider(
                label: 'Age',
                value: sliderValue,
                min: 18,
                max: 80,
                unit: ' years',
                onChanged: (value) => setState(() => sliderValue = value),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Text Input
            _buildSection(
              'Modern Text Input',
              Column(
                children: [
                  ModernTextInput(
                    label: 'Full Name',
                    hint: 'Enter your full name',
                    onChanged: (value) => setState(() => textValue = value),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ModernTextInput(
                    label: 'Email',
                    hint: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: const Icon(Icons.email, color: Colors.white54),
                    onChanged: (value) {},
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Multi-Select
            _buildSection(
              'Modern Multi-Select',
              ModernMultiSelect<String>(
                title: 'Fitness Goals',
                subtitle: 'Select your primary fitness goals',
                maxSelections: 3,
                options: const [
                  ModernMultiSelectOption(
                    value: 'weight_loss',
                    title: 'Weight Loss',
                    subtitle: 'Burn calories and lose weight',
                    icon: Icons.trending_down,
                  ),
                  ModernMultiSelectOption(
                    value: 'muscle_gain',
                    title: 'Muscle Gain',
                    subtitle: 'Build strength and muscle mass',
                    icon: Icons.fitness_center,
                  ),
                  ModernMultiSelectOption(
                    value: 'endurance',
                    title: 'Endurance',
                    subtitle: 'Improve cardiovascular fitness',
                    icon: Icons.directions_run,
                  ),
                  ModernMultiSelectOption(
                    value: 'flexibility',
                    title: 'Flexibility',
                    subtitle: 'Increase range of motion',
                    icon: Icons.self_improvement,
                  ),
                ],
                selectedValues: selectedOptions,
                onSelectionChanged: (values) => setState(() => selectedOptions = values),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Rating - Stars
            _buildSection(
              'Star Rating',
              ModernRating(
                label: 'Rate your fitness level',
                value: starRating,
                maxRating: 5,
                type: RatingType.stars,
                onRatingChanged: (rating) => setState(() => starRating = rating),
                allowZero: true,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Rating - Scale
            _buildSection(
              'Scale Rating',
              ModernRating(
                label: 'How motivated are you?',
                value: scaleRating,
                maxRating: 10,
                type: RatingType.scale,
                scaleLabels: const ['Low', 'Medium', 'High'],
                onRatingChanged: (rating) => setState(() => scaleRating = rating),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Rating - Emoji
            _buildSection(
              'Emoji Rating',
              ModernRating(
                label: 'How do you feel about working out?',
                value: emojiRating,
                maxRating: 5,
                type: RatingType.emoji,
                emojiList: const ['😫', '😕', '😐', '😊', '🤩'],
                onRatingChanged: (rating) => setState(() => emojiRating = rating),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Modern Rating - Thumbs
            _buildSection(
              'Thumbs Rating',
              ModernRating(
                label: 'Do you like group workouts?',
                value: thumbsRating,
                maxRating: 2,
                type: RatingType.thumbs,
                onRatingChanged: (rating) => setState(() => thumbsRating = rating),
                allowZero: true,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Summary
            _buildSection(
              'Current Values',
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF2A2D3A).withOpacity(0.6),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Selected Choice: $selectedChoice', style: const TextStyle(color: Colors.white)),
                    Text('Slider Value: ${sliderValue.round()}', style: const TextStyle(color: Colors.white)),
                    Text('Text Value: $textValue', style: const TextStyle(color: Colors.white)),
                    Text('Selected Options: ${selectedOptions.join(', ')}', style: const TextStyle(color: Colors.white)),
                    Text('Star Rating: $starRating', style: const TextStyle(color: Colors.white)),
                    Text('Scale Rating: $scaleRating', style: const TextStyle(color: Colors.white)),
                    Text('Emoji Rating: $emojiRating', style: const TextStyle(color: Colors.white)),
                    Text('Thumbs Rating: $thumbsRating', style: const TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }
}