import 'dart:async';
import 'package:flutter/material.dart';
import 'package:openfit/models/workout.dart';

class WorkoutTimerScreen extends StatefulWidget {
  final Workout workout;
  final bool isPreviewMode;

  const WorkoutTimerScreen({
    super.key,
    required this.workout,
    this.isPreviewMode = false,
  });

  @override
  State<WorkoutTimerScreen> createState() => _WorkoutTimerScreenState();
}

class _WorkoutTimerScreenState extends State<WorkoutTimerScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  Timer? _timer;
  
  int currentExerciseIndex = 0;
  int timeRemaining = 0;
  bool isResting = false;
  bool isStarted = false;
  bool isPaused = false;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
    _setupExercise();
  }

  void _setupExercise() {
    if (currentExerciseIndex < widget.workout.exercises.length) {
      final exercise = widget.workout.exercises[currentExerciseIndex];
      // In preview mode, reduce timer to 3 seconds for quick preview
      timeRemaining = widget.isPreviewMode ? 3 : exercise.duration;
      isResting = false;
    }
  }

  void _startTimer() {
    setState(() => isStarted = true);
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!isPaused) {
        setState(() {
          timeRemaining--;
          _updateProgress();
        });

        if (timeRemaining <= 0) {
          _onTimerComplete();
        }
      }
    });
  }

  void _updateProgress() {
    if (currentExerciseIndex < widget.workout.exercises.length) {
      final exercise = widget.workout.exercises[currentExerciseIndex];
      final totalTime = isResting ? exercise.restTime : exercise.duration;
      final progress = 1 - (timeRemaining / totalTime);
      _progressController.animateTo(progress);
    }
  }

  void _onTimerComplete() {
    if (!isResting && currentExerciseIndex < widget.workout.exercises.length) {
      // Switch to rest period
      final exercise = widget.workout.exercises[currentExerciseIndex];
      setState(() {
        isResting = true;
        // In preview mode, reduce rest time to 2 seconds
        timeRemaining = widget.isPreviewMode ? 2 : exercise.restTime;
      });
      _progressController.reset();
    } else {
      // Move to next exercise or finish workout
      if (currentExerciseIndex < widget.workout.exercises.length - 1) {
        setState(() {
          currentExerciseIndex++;
          isResting = false;
        });
        _setupExercise();
        _progressController.reset();
      } else {
        // Workout completed
        _finishWorkout();
      }
    }
  }

  void _pauseResume() {
    setState(() => isPaused = !isPaused);
  }

  void _finishWorkout() {
    _timer?.cancel();
    _showCompletionDialog();
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(widget.isPreviewMode ? '👀 Preview Complete!' : '🎉 Workout Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.isPreviewMode 
                  ? 'You\'ve previewed all the workout screens!' 
                  : 'Congratulations! You\'ve completed your workout.'
            ),
            const SizedBox(height: 16),
            if (!widget.isPreviewMode) 
              Text('Estimated calories burned: ${widget.workout.caloriesBurn}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final exercise = currentExerciseIndex < widget.workout.exercises.length
        ? widget.workout.exercises[currentExerciseIndex]
        : null;

    return Scaffold(
      body: SafeArea(
        child: exercise == null
            ? const Center(child: Text('Workout Complete!'))
            : Column(
                children: [
                  // Header
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close_rounded),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                widget.isPreviewMode 
                                    ? 'PREVIEW MODE - Exercise ${currentExerciseIndex + 1} of ${widget.workout.exercises.length}'
                                    : 'Exercise ${currentExerciseIndex + 1} of ${widget.workout.exercises.length}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: widget.isPreviewMode 
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  fontWeight: widget.isPreviewMode 
                                      ? FontWeight.bold 
                                      : FontWeight.normal,
                                ),
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: (currentExerciseIndex + 1) / widget.workout.exercises.length,
                                backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                                valueColor: AlwaysStoppedAnimation(
                                  widget.isPreviewMode 
                                      ? theme.colorScheme.secondary
                                      : theme.colorScheme.primary
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 48),
                      ],
                    ),
                  ),

                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Exercise Name
                        Text(
                          isResting ? 'Rest' : exercise.name,
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        // Show next exercise during rest
                        if (isResting && currentExerciseIndex < widget.workout.exercises.length - 1) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Next: ${widget.workout.exercises[currentExerciseIndex + 1].name}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.secondary,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                        const SizedBox(height: 16),

                        if (!isResting) ...[
                          Text(
                            exercise.description,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),
                        ],

                        // Timer Circle
                        SizedBox(
                          width: 200,
                          height: 200,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              SizedBox(
                                width: 200,
                                height: 200,
                                child: AnimatedBuilder(
                                  animation: _progressAnimation,
                                  builder: (context, child) {
                                    return CircularProgressIndicator(
                                      value: _progressAnimation.value,
                                      strokeWidth: 8,
                                      backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                                      valueColor: AlwaysStoppedAnimation(
                                        isResting 
                                            ? theme.colorScheme.secondary
                                            : theme.colorScheme.primary,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${timeRemaining ~/ 60}:${(timeRemaining % 60).toString().padLeft(2, '0')}',
                                    style: theme.textTheme.displayLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                  Text(
                                    isResting ? 'REST' : 'WORK',
                                    style: theme.textTheme.labelLarge?.copyWith(
                                      color: isResting 
                                          ? theme.colorScheme.secondary
                                          : theme.colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 32),

                        if (!isResting && exercise.instructions.isNotEmpty) ...[
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 32),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Instructions:',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onPrimaryContainer,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ...exercise.instructions.take(2).map(
                                  (instruction) => Padding(
                                    padding: const EdgeInsets.only(bottom: 4),
                                    child: Text(
                                      '• $instruction',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: theme.colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Preview Mode Info
                        if (widget.isPreviewMode) ...[
                          const SizedBox(height: 16),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 32),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.secondaryContainer,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: theme.colorScheme.secondary,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.preview_rounded,
                                  color: theme.colorScheme.secondary,
                                  size: 24,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Preview Mode',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSecondaryContainer,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Timers are shortened to quickly preview all screens',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSecondaryContainer.withValues(alpha: 0.8),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Control Buttons
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: widget.isPreviewMode 
                        ? Column(
                            children: [
                              if (isStarted) ...[
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    FloatingActionButton(
                                      onPressed: _pauseResume,
                                      backgroundColor: theme.colorScheme.secondary,
                                      foregroundColor: theme.colorScheme.onSecondary,
                                      child: Icon(
                                        isPaused ? Icons.play_arrow_rounded : Icons.pause_rounded,
                                      ),
                                    ),
                                    FloatingActionButton.extended(
                                      onPressed: _onTimerComplete,
                                      backgroundColor: theme.colorScheme.primary,
                                      foregroundColor: theme.colorScheme.onPrimary,
                                      icon: const Icon(Icons.skip_next_rounded),
                                      label: Text(isResting ? 'Skip Rest' : 'Skip Exercise'),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    // Skip to end of workout
                                    _timer?.cancel();
                                    _finishWorkout();
                                  },
                                  icon: const Icon(Icons.fast_forward_rounded),
                                  label: const Text('Skip to End'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.colorScheme.tertiary,
                                    foregroundColor: theme.colorScheme.onTertiary,
                                    minimumSize: const Size(200, 40),
                                  ),
                                ),
                              ] else ...[
                                FloatingActionButton.extended(
                                  onPressed: _startTimer,
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: theme.colorScheme.onPrimary,
                                  icon: const Icon(Icons.play_arrow_rounded),
                                  label: const Text('Start Preview'),
                                ),
                                const SizedBox(height: 12),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    // Skip to end of workout directly
                                    _finishWorkout();
                                  },
                                  icon: const Icon(Icons.fast_forward_rounded),
                                  label: const Text('Skip to End'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.colorScheme.tertiary,
                                    foregroundColor: theme.colorScheme.onTertiary,
                                    minimumSize: const Size(200, 40),
                                  ),
                                ),
                              ],
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              if (isStarted) ...[
                                FloatingActionButton(
                                  onPressed: _pauseResume,
                                  backgroundColor: theme.colorScheme.secondary,
                                  foregroundColor: theme.colorScheme.onSecondary,
                                  child: Icon(
                                    isPaused ? Icons.play_arrow_rounded : Icons.pause_rounded,
                                  ),
                                ),
                                FloatingActionButton.extended(
                                  onPressed: _onTimerComplete,
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: theme.colorScheme.onPrimary,
                                  icon: const Icon(Icons.skip_next_rounded),
                                  label: const Text('Skip'),
                                ),
                              ] else ...[
                                FloatingActionButton.extended(
                                  onPressed: _startTimer,
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: theme.colorScheme.onPrimary,
                                  icon: const Icon(Icons.play_arrow_rounded),
                                  label: const Text('Start'),
                                ),
                              ],
                            ],
                          ),
                  ),
                ],
              ),
      ),
    );
  }
}