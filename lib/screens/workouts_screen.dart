import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/core/error_handler.dart';
import 'package:openfit/data/workout_data.dart';
import 'package:openfit/providers/app_state.dart';
import 'package:openfit/providers/workout_providers.dart';
import 'package:openfit/screens/active_workout_screen.dart';
import 'package:openfit/widgets/error_display.dart';
import 'package:openfit/widgets/top_knob_bar.dart';

class WorkoutsScreen extends ConsumerStatefulWidget {
  const WorkoutsScreen({super.key});

  @override
  ConsumerState<WorkoutsScreen> createState() => _WorkoutsScreenState();
}

class _WorkoutsScreenState extends ConsumerState<WorkoutsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<WorkoutCategory> workoutCategories = [
    WorkoutCategory(
      title: 'Strength Training',
      subtitle: '💪 Build Power',
      exerciseCount: 256,
      duration: '45-60 min',
      icon: Icons.fitness_center,
      imageUrl: 'https://pixabay.com/get/g0e63b6782921a272336912270e3d5863d8fbdc9455ecb26ed35aee50d7b3f05e50bcf3fde0712e32fcc838df2db459ae74b09f3dcc8c15908d0e57fde25ea3e3_1280.jpg',
      gradientColors: [const Color(0xFFFF6B35), const Color(0xFFFF8A65)],
    ),
    WorkoutCategory(
      title: 'Cardio & HIIT',
      subtitle: '❤️ Boost Endurance',
      exerciseCount: 189,
      duration: '30-45 min',
      icon: Icons.directions_run,
      imageUrl: 'https://pixabay.com/get/g5d765323d929ef2b2f4ba42f4d2deaf4c4a9a9e692cbbbad2b07498189cb9ba1b5d5ab4a77b437d7783137b074d6e2d1fb62d2415a89b22ab3a47a2195385c4b_1280.jpg',
      gradientColors: [const Color(0xFF2196F3), const Color(0xFF64B5F6)],
    ),
    WorkoutCategory(
      title: 'Yoga & Mindfulness',
      subtitle: '🧘 Find Balance',
      exerciseCount: 134,
      duration: '20-60 min',
      icon: Icons.self_improvement,
      imageUrl: 'https://pixabay.com/get/gb267ae44480f3f8cbd2b108e5ccc74bb0d3f0837a6cb7ed8c7b6f17d2793f385465e68b3d8a6083e9c73165b26a37b0b6978ac1e9bd695f9030b17d378331735_1280.jpg',
      gradientColors: [const Color(0xFF4CAF50), const Color(0xFF81C784)],
    ),
    WorkoutCategory(
      title: 'Flexibility',
      subtitle: '🤸 Improve Mobility',
      exerciseCount: 97,
      duration: '15-30 min',
      icon: Icons.accessibility_new,
      imageUrl: 'https://pixabay.com/get/g7fec6d7812209630d0cf75e1f318fd65308f3b3e1b20e7e4b0f8290c8da1a3d6af20ed157e25bb392f3a6352dff480c535e534760cffe3dc7bbe36abd5c57184_1280.jpg',
      gradientColors: [const Color(0xFF9C27B0), const Color(0xFFBA68C8)],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final workoutsAsync = ref.watch(workoutsProvider);
    final workoutStats = ref.watch(workoutStatsProvider);
    
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: workoutsAsync.when(
          data: (workouts) => AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) => FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Top Knob Bar
                      const TopKnobBar(title: 'Workouts'),
                      const SizedBox(height: 24),

                      // Hero Section
                      _buildHeroSection(context),
                      const SizedBox(height: 24),

                      // Quick Stats
                      workoutStats.when(
                        data: (stats) => _buildQuickStats(context, stats),
                        loading: () => const Center(child: CircularProgressIndicator()),
                        error: (_, __) => const SizedBox.shrink(),
                      ),
                      const SizedBox(height: 24),

                      // Featured Workout
                      _buildFeaturedWorkout(context),
                      const SizedBox(height: 24),

                      // Categories Section
                      _buildCategoriesSection(context),
                      const SizedBox(height: 24),

                      // Recent Activity
                      _buildRecentActivity(context),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ),
          loading: () => const LoadingDisplay(message: 'Loading workouts...'),
          error: (error, stackTrace) => ErrorDisplay(
            error: error,
            onRetry: () => ref.refresh(workoutsProvider),
            message: 'Failed to load workouts. Please try again.',
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Ready to Sweat?',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Choose your workout and let\'s crush those fitness goals! 🔥',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.local_fire_department,
              size: 32,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, WorkoutStats stats) {
    return Row(
      children: [
        _buildStatCard(context, '🏆', '${stats.totalWorkouts}', 'Total\nWorkouts'),
        const SizedBox(width: 12),
        _buildStatCard(context, '🔥', '${stats.currentStreak}', 'Day\nStreak'),
        const SizedBox(width: 12),
        _buildStatCard(context, '⏰', '${stats.totalMinutes}', 'Minutes\nActive'),
      ],
    );
  }

  Widget _buildStatCard(BuildContext context, String emoji, String value, String label) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Text(emoji, style: const TextStyle(fontSize: 20)),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.7),
                height: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedWorkout(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF6B35), Color(0xFFFF8A65)],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Workout of the Day',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Full Body HIIT Blast',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '25 min • High Intensity',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Theme.of(context).colorScheme.primary,
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Workout Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {},
              child: Text(
                'View All',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.85,
          ),
          itemCount: workoutCategories.length,
          itemBuilder: (context, index) {
            return WorkoutCategoryCard(
              category: workoutCategories[index],
              delay: Duration(milliseconds: 100 * index),
            );
          },
        ),
      ],
    );
  }

  Widget _buildRecentActivity(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildActivityItem(context, '🏃‍♂️', 'Morning Run', '35 min', '2 hours ago'),
        const SizedBox(height: 12),
        _buildActivityItem(context, '💪', 'Strength Training', '50 min', 'Yesterday'),
        const SizedBox(height: 12),
        _buildActivityItem(context, '🧘‍♀️', 'Yoga Flow', '30 min', '2 days ago'),
      ],
    );
  }

  Widget _buildActivityItem(BuildContext context, String emoji, String title, String duration, String time) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '$duration • $time',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.more_horiz,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ],
      ),
    );
  }
}

class WorkoutCategory {
  final String title;
  final String subtitle;
  final int exerciseCount;
  final String duration;
  final IconData icon;
  final String imageUrl;
  final List<Color> gradientColors;

  WorkoutCategory({
    required this.title,
    required this.subtitle,
    required this.exerciseCount,
    required this.duration,
    required this.icon,
    required this.imageUrl,
    required this.gradientColors,
  });
}

class WorkoutCategoryCard extends ConsumerStatefulWidget {
  final WorkoutCategory category;
  final Duration delay;

  const WorkoutCategoryCard({
    super.key,
    required this.category,
    this.delay = Duration.zero,
  });

  @override
  ConsumerState<WorkoutCategoryCard> createState() => _WorkoutCategoryCardState();
}

class _WorkoutCategoryCardState extends ConsumerState<WorkoutCategoryCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _startWorkout(BuildContext context, WidgetRef ref) {
    ErrorHandler.safeAsyncCall(
      () async {
        final session = WorkoutData.warmUpWorkout.copyWith(
          workoutName: widget.category.title,
          workoutType: widget.category.title,
          imageUrl: widget.category.imageUrl,
        );
        
        // Update current workout state
        ref.read(currentWorkoutProvider.notifier).startWorkout(session);
        
        // Navigate to active workout screen
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ActiveWorkoutScreen(initialSession: session),
            ),
          );
        }
      },
      context: 'Starting workout: ${widget.category.title}',
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) => Transform.scale(
        scale: _scaleAnimation.value,
        child: GestureDetector(
          onTapDown: (_) {
            setState(() => _isHovered = true);
            _hoverController.forward();
          },
          onTapUp: (_) {
            setState(() => _isHovered = false);
            _hoverController.reverse();
            _startWorkout(context, ref);
          },
          onTapCancel: () {
            setState(() => _isHovered = false);
            _hoverController.reverse();
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: widget.category.gradientColors.first.withValues(alpha: 0.3),
                  blurRadius: _isHovered ? 20 : 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Background Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    height: double.infinity,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: NetworkImage(widget.category.imageUrl),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                // Gradient Overlay
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                // Content
                Positioned(
                  left: 16,
                  right: 16,
                  bottom: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: widget.category.gradientColors,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          widget.category.icon,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.category.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.category.subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            '${widget.category.exerciseCount} exercises',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 11,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 2,
                            height: 2,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.7),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            widget.category.duration,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}