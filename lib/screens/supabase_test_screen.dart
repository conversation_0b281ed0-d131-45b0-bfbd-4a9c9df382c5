import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/providers/supabase_providers.dart';
import 'package:openfit/widgets/error_display.dart';

/// Test screen to verify Supabase integration
class SupabaseTestScreen extends ConsumerWidget {
  const SupabaseTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final exercisesAsync = ref.watch(allExercisesProvider);
    final categoriesAsync = ref.watch(exerciseCategoriesProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Integration Test'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Categories Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Exercise Categories',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    categoriesAsync.when(
                      data: (categories) => Wrap(
                        spacing: 8,
                        children: categories
                            .take(10)
                            .map((category) => Chip(label: Text(category)))
                            .toList(),
                      ),
                      loading: () => const CircularProgressIndicator(),
                      error: (error, _) => CompactErrorDisplay(
                        error: error,
                        onRetry: () => ref.refresh(exerciseCategoriesProvider),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Exercises Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sample Exercises',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    exercisesAsync.when(
                      data: (exercises) => Column(
                        children: exercises
                            .take(5)
                            .map((exercise) => ListTile(
                                  title: Text(exercise.name),
                                  subtitle: Text(
                                    '${exercise.primaryMuscle ?? 'Unknown'} • ${exercise.equipment ?? 'No equipment'}',
                                  ),
                                  trailing: exercise.videoUrl != null
                                      ? const Icon(Icons.play_circle_outline)
                                      : null,
                                ))
                            .toList(),
                      ),
                      loading: () => const LoadingDisplay(
                        message: 'Loading exercises from Supabase...',
                      ),
                      error: (error, _) => ErrorDisplay(
                        error: error,
                        onRetry: () => ref.refresh(allExercisesProvider),
                        message: 'Failed to load exercises from database',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Database Stats
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Database Statistics',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    exercisesAsync.when(
                      data: (exercises) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Total Exercises: ${exercises.length}'),
                          Text('With Video: ${exercises.where((e) => e.videoUrl != null).length}'),
                          Text('With Instructions: ${exercises.where((e) => e.instructions != null).length}'),
                        ],
                      ),
                      loading: () => const Text('Loading stats...'),
                      error: (_, __) => const Text('Error loading stats'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Refresh Button
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.refresh(allExercisesProvider);
                  ref.refresh(exerciseCategoriesProvider);
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh Data'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}