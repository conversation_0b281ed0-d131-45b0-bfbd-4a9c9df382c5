import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/core/error_handler.dart';
import 'package:openfit/data/workout_data.dart';
import 'package:openfit/providers/app_state.dart';
import 'package:openfit/providers/workout_providers.dart';
import 'package:openfit/screens/active_workout_screen.dart';
import 'package:openfit/widgets/modern_stat_card.dart';
import 'package:openfit/widgets/modern_workout_card.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController, 
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startWorkout(String workoutType) {
    ErrorHandler.safeAsyncCall(
      () async {
        // Create a sample workout session
        final session = WorkoutData.warmUpWorkout.copyWith(
          workoutName: workoutType,
          workoutType: workoutType,
        );
        
        // Update current workout state
        ref.read(currentWorkoutProvider.notifier).startWorkout(session);
        
        // Navigate to active workout screen
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ActiveWorkoutScreen(initialSession: session),
            ),
          );
        }
      },
      context: 'Starting workout: $workoutType',
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final workoutStats = ref.watch(workoutStatsProvider);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top Header with Profile - Movemate Style
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: const Color(0xFFFF6B47),
                            width: 2,
                          ),
                        ),
                        child: const CircleAvatar(
                          radius: 20,
                          backgroundImage: NetworkImage(
                            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Adam Smith',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            'Time to workout',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: const Color(0xFF9CA3AF),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2A2D3A),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.notifications_outlined,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),

                  // Statistics Cards Row
                  Row(
                    children: [
                      Expanded(
                        child: ModernStatCard(
                          title: 'Calories',
                          value: '1200',
                          unit: 'Kcal',
                          icon: Icons.local_fire_department,
                          iconColor: const Color(0xFFFF6B47),
                          valueColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ModernStatCard(
                          title: 'Steps',
                          value: '9560',
                          unit: 'Kcal',
                          icon: Icons.directions_walk,
                          iconColor: const Color(0xFF4ADE80),
                          valueColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: ModernStatCard(
                          title: 'Heart Beats',
                          value: '73',
                          unit: 'bpm',
                          icon: Icons.favorite,
                          iconColor: const Color(0xFFEF4444),
                          valueColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ModernStatCard(
                          title: 'Workouts',
                          value: workoutStats.when(
                            data: (stats) => '${stats.totalWorkouts}',
                            loading: () => '14',
                            error: (_, __) => '14',
                          ),
                          unit: '/20',
                          icon: Icons.fitness_center,
                          iconColor: const Color(0xFF60A5FA),
                          valueColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),

                  // Today's Workout Card
                  TodaysWorkoutCard(
                    workoutName: 'Full Body Workout',
                    difficulty: 'Advanced',
                    duration: '60 min',
                    imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
                    onTap: () => _startWorkout('Full Body'),
                  ),
                  
                  const SizedBox(height: 24),

                  // Workout Categories Grid - Movemate Style
                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 0.85,
                    children: [
                      ModernWorkoutCard(
                        title: 'Strength',
                        subtitle: '250 Exercises',
                        icon: Icons.fitness_center,
                        accentColor: const Color(0xFFFF6B47),
                        duration: '30-60 min',
                        difficulty: 'Beginner',
                        onTap: () => _startWorkout('Strength'),
                      ),
                      ModernWorkoutCard(
                        title: 'Stamina',
                        subtitle: '174 Exercises',
                        icon: Icons.directions_run,
                        accentColor: const Color(0xFF4ADE80),
                        duration: '20-45 min',
                        difficulty: 'Intermediate',
                        onTap: () => _startWorkout('Stamina'),
                      ),
                      ModernWorkoutCard(
                        title: 'Cardio & Care',
                        subtitle: '97 Exercises',
                        icon: Icons.favorite,
                        accentColor: const Color(0xFFEF4444),
                        duration: '15-30 min',
                        difficulty: 'All Levels',
                        onTap: () => _startWorkout('Cardio'),
                      ),
                      ModernWorkoutCard(
                        title: 'Yoga',
                        subtitle: '68 Exercises',
                        icon: Icons.self_improvement,
                        accentColor: const Color(0xFF8B5CF6),
                        duration: '30-90 min',
                        difficulty: 'Beginner',
                        onTap: () => _startWorkout('Yoga'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 100), // Space for bottom navigation
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }


}