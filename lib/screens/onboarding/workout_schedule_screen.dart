import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';
import 'package:openfit/widgets/onboarding/text_input_field.dart';
import 'package:openfit/widgets/onboarding/slider_input.dart';

class WorkoutScheduleScreen extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback? onBack;
  final VoidCallback? onNext;
  final VoidCallback? onSkip;
  final ValueChanged<OnboardingData> onDataChanged;

  const WorkoutScheduleScreen({
    super.key,
    required this.data,
    required this.onDataChanged,
    this.onBack,
    this.onNext,
    this.onSkip,
  });

  @override
  State<WorkoutScheduleScreen> createState() => _WorkoutScheduleScreenState();
}

class _WorkoutScheduleScreenState extends State<WorkoutScheduleScreen> {
  late WorkoutsPerWeek? _workoutsPerWeek;
  late double _workoutDurationMinutes;
  late bool _optimizeWorkoutTime;
  late List<int> _preferredDays;
  late bool _optimizeWorkoutDays;

  static const List<String> durationLabels = [
    '15min', '30min', '45min', '60min', '90min', '120min'
  ];

  @override
  void initState() {
    super.initState();
    _workoutsPerWeek = widget.data.workoutsPerWeek;
    _workoutDurationMinutes = _getDurationSliderValue(widget.data.workoutDuration);
    _optimizeWorkoutTime = widget.data.optimizeWorkoutTime ?? false;
    _preferredDays = _convertDayOfWeekToInt(widget.data.preferredDays);
    _optimizeWorkoutDays = widget.data.optimizeWorkoutDays ?? false;
  }

  double _getDurationSliderValue(WorkoutDuration? duration) {
    if (duration == null) return 1.0; // Default to 30min
    switch (duration) {
      case WorkoutDuration.minutes15:
        return 0.0;
      case WorkoutDuration.minutes30:
        return 1.0;
      case WorkoutDuration.minutes45:
        return 2.0;
      case WorkoutDuration.minutes60:
        return 3.0;
      case WorkoutDuration.minutes90:
        return 4.0;
      case WorkoutDuration.minutes120:
        return 5.0;
    }
  }

  WorkoutDuration _getWorkoutDurationFromSlider(double value) {
    switch (value.round()) {
      case 0:
        return WorkoutDuration.minutes15;
      case 1:
        return WorkoutDuration.minutes30;
      case 2:
        return WorkoutDuration.minutes45;
      case 3:
        return WorkoutDuration.minutes60;
      case 4:
        return WorkoutDuration.minutes90;
      case 5:
        return WorkoutDuration.minutes120;
      default:
        return WorkoutDuration.minutes30;
    }
  }

  List<int> _convertDayOfWeekToInt(List<DayOfWeek> days) {
    return days.map((day) {
      switch (day) {
        case DayOfWeek.sunday:
          return 0;
        case DayOfWeek.monday:
          return 1;
        case DayOfWeek.tuesday:
          return 2;
        case DayOfWeek.wednesday:
          return 3;
        case DayOfWeek.thursday:
          return 4;
        case DayOfWeek.friday:
          return 5;
        case DayOfWeek.saturday:
          return 6;
      }
    }).toList();
  }

  List<DayOfWeek> _convertIntToDayOfWeek(List<int> days) {
    return days.map((day) {
      switch (day) {
        case 0:
          return DayOfWeek.sunday;
        case 1:
          return DayOfWeek.monday;
        case 2:
          return DayOfWeek.tuesday;
        case 3:
          return DayOfWeek.wednesday;
        case 4:
          return DayOfWeek.thursday;
        case 5:
          return DayOfWeek.friday;
        case 6:
          return DayOfWeek.saturday;
        default:
          return DayOfWeek.monday;
      }
    }).toList();
  }

  void _updateData() {
    final updatedData = widget.data.copyWith(
      workoutsPerWeek: _workoutsPerWeek,
      workoutDuration: _getWorkoutDurationFromSlider(_workoutDurationMinutes),
      optimizeWorkoutTime: _optimizeWorkoutTime,
      preferredDays: _convertIntToDayOfWeek(_preferredDays),
      optimizeWorkoutDays: _optimizeWorkoutDays,
    );
    widget.onDataChanged(updatedData);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            OnboardingHeader(
              title: "Your Workout Schedule",
              subtitle: "Let's plan your ideal workout routine",
              onBack: widget.onBack,
              onSkip: widget.onSkip,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section Title
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        "Workout Preferences",
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Workouts per week
                    DropdownSelector<WorkoutsPerWeek>(
                      label: "What should your number of workouts per week goal be?",
                      value: _workoutsPerWeek,
                      items: WorkoutsPerWeek.values,
                      getLabel: (workouts) => workouts.displayName,
                      onChanged: (workouts) {
                        setState(() {
                          _workoutsPerWeek = workouts;
                        });
                        _updateData();
                      },
                      hint: "Select workouts per week",
                    ),
                    const SizedBox(height: 24),

                    // Workout duration slider
                    SliderInput(
                      title: "How long would you like your workout to be?",
                      subtitle: "Choose your preferred workout duration",
                      value: _workoutDurationMinutes,
                      min: 0,
                      max: 5,
                      divisions: 5,
                      labels: durationLabels,
                      onChanged: (value) {
                        setState(() {
                          _workoutDurationMinutes = value;
                        });
                        _updateData();
                      },
                    ),
                    const SizedBox(height: 16),

                    // Optimize time checkbox
                    CheckboxListTile(
                      title: Text(
                        "No preference, optimize the time for me",
                        style: theme.textTheme.bodyMedium,
                      ),
                      value: _optimizeWorkoutTime,
                      onChanged: (value) {
                        setState(() {
                          _optimizeWorkoutTime = value ?? false;
                        });
                        _updateData();
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 24),

                    // Day preference picker
                    DaySelector(
                      label: "Day preference picker",
                      selectedDays: _preferredDays,
                      onChanged: (days) {
                        setState(() {
                          _preferredDays = days;
                        });
                        _updateData();
                      },
                    ),
                    const SizedBox(height: 16),

                    // Optimize days checkbox
                    CheckboxListTile(
                      title: Text(
                        "No preference, optimize the days for me",
                        style: theme.textTheme.bodyMedium,
                      ),
                      value: _optimizeWorkoutDays,
                      onChanged: (value) {
                        setState(() {
                          _optimizeWorkoutDays = value ?? false;
                        });
                        _updateData();
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: theme.colorScheme.primary,
                    ),
                    
                    SizedBox(height: screenHeight * 0.15),
                  ],
                ),
              ),
            ),
            
            // Bottom Navigation
            Container(
              padding: const EdgeInsets.all(24),
              child: OnboardingButton(
                text: "Continue",
                onPressed: widget.onNext,
                isEnabled: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}