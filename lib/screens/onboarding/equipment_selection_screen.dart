import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';
import 'package:openfit/widgets/onboarding/multi_select_cards.dart';

class EquipmentSelectionScreen extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback? onBack;
  final VoidCallback? onNext;
  final VoidCallback? onSkip;
  final ValueChanged<OnboardingData> onDataChanged;

  const EquipmentSelectionScreen({
    super.key,
    required this.data,
    required this.onDataChanged,
    this.onBack,
    this.onNext,
    this.onSkip,
  });

  @override
  State<EquipmentSelectionScreen> createState() => _EquipmentSelectionScreenState();
}

class _EquipmentSelectionScreenState extends State<EquipmentSelectionScreen> {
  late List<Equipment> _availableEquipment;

  @override
  void initState() {
    super.initState();
    _availableEquipment = List.from(widget.data.availableEquipment);
  }

  void _updateData() {
    final updatedData = widget.data.copyWith(
      availableEquipment: _availableEquipment,
    );
    widget.onDataChanged(updatedData);
  }

  void _onEquipmentSelected(List<Equipment> equipment) {
    setState(() {
      _availableEquipment = equipment;
    });
    _updateData();
  }

  String _getEquipmentDescription(Equipment equipment) {
    switch (equipment) {
      case Equipment.dumbbells:
        return 'Adjustable or fixed weight dumbbells';
      case Equipment.barbell:
        return 'Olympic barbell with weight plates';
      case Equipment.kettlebells:
        return 'Cast iron or steel kettlebells';
      case Equipment.resistanceBands:
        return 'Elastic bands for resistance training';
      case Equipment.pullUpBar:
        return 'Doorway or mounted pull-up bar';
      case Equipment.benchPress:
        return 'Weight bench for pressing exercises';
      case Equipment.treadmill:
        return 'Motorized treadmill for cardio';
      case Equipment.elliptical:
        return 'Elliptical machine for low-impact cardio';
      case Equipment.stationaryBike:
        return 'Exercise bike for cycling workouts';
      case Equipment.yogaMat:
        return 'Mat for floor exercises and stretching';
      case Equipment.foamRoller:
        return 'Recovery tool for muscle massage';
      case Equipment.medicineeBall:
        return 'Weighted ball for functional training';
      case Equipment.jumpRope:
        return 'Rope for cardio and coordination';
      case Equipment.gymAccess:
        return 'Access to a full gym facility';
      case Equipment.noEquipment:
        return 'Bodyweight exercises only';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            OnboardingHeader(
              title: "Select Equipment",
              subtitle: "What equipment do you have access to?",
              onBack: widget.onBack,
              onSkip: widget.onSkip,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Equipment Selection
                    MultiSelectCards<Equipment>(
                      title: "Select all equipment you have access to",
                      subtitle: "We'll customize your workouts based on your available equipment",
                      items: Equipment.values,
                      selectedItems: _availableEquipment,
                      getTitle: (equipment) => equipment.displayName,
                      getDescription: (equipment) => _getEquipmentDescription(equipment),
                      onSelectionChanged: _onEquipmentSelected,
                    ),
                    
                    SizedBox(height: screenHeight * 0.15),
                  ],
                ),
              ),
            ),
            
            // Bottom Navigation
            Container(
              padding: const EdgeInsets.all(24),
              child: OnboardingButton(
                text: "Continue",
                onPressed: widget.onNext,
                isEnabled: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}