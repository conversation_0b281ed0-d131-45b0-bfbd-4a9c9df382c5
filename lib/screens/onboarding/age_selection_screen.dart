import 'package:flutter/material.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';

class AgeSelectionScreen extends StatefulWidget {
  final int? selectedAge;
  final Function(int) onAgeSelected;
  final VoidCallback onBack;
  final VoidCallback onNext;
  final VoidCallback onSkip;

  const AgeSelectionScreen({
    super.key,
    this.selectedAge,
    required this.onAgeSelected,
    required this.onBack,
    required this.onNext,
    required this.onSkip,
  });

  @override
  State<AgeSelectionScreen> createState() => _AgeSelectionScreenState();
}

class _AgeSelectionScreenState extends State<AgeSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  late ScrollController _scrollController;
  int? _selectedAge;
  
  // Age range from 13 to 80
  static const int minAge = 13;
  static const int maxAge = 80;
  static const double itemHeight = 60.0;

  @override
  void initState() {
    super.initState();
    _selectedAge = widget.selectedAge ?? 25; // Default to 25
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    // Initialize scroll controller to center on selected age
    _scrollController = ScrollController(
      initialScrollOffset: (_selectedAge! - minAge) * itemHeight,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _selectAge(int age) {
    setState(() {
      _selectedAge = age;
    });
    widget.onAgeSelected(age);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header
          OnboardingHeader(
            title: 'How old are you?',
            subtitle: 'This help us to create your personalized exercise.',
            onBack: widget.onBack,
            onSkip: widget.onSkip,
          ),

          // Main Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 40),

                  // Age Picker
                  Expanded(
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Center(
                          child: Container(
                            width: 280,
                            height: 360,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  const Color(0xFF2A2D3A).withValues(alpha: 0.3),
                                  const Color(0xFF3D4A3D).withValues(alpha: 0.5),
                                  const Color(0xFF2A2D3A).withValues(alpha: 0.3),
                                  Colors.transparent,
                                ],
                                stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                              ),
                            ),
                            child: Stack(
                              children: [
                                // Selection indicator
                                Positioned(
                                  top: 150,
                                  left: 0,
                                  right: 0,
                                  height: itemHeight,
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(horizontal: 20),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(15),
                                      border: Border.all(
                                        color: theme.colorScheme.primary.withValues(alpha: 0.5),
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                
                                // Age list
                                NotificationListener<ScrollNotification>(
                                  onNotification: (notification) {
                                    if (notification is ScrollEndNotification) {
                                      final offset = _scrollController.offset;
                                      final index = (offset / itemHeight).round();
                                      final age = minAge + index;
                                      if (age >= minAge && age <= maxAge && age != _selectedAge) {
                                        _selectAge(age);
                                      }
                                    }
                                    return true;
                                  },
                                  child: ListView.builder(
                                    controller: _scrollController,
                                    physics: const FixedExtentScrollPhysics(),
                                    itemExtent: itemHeight,
                                    padding: EdgeInsets.symmetric(
                                      vertical: (360 - itemHeight) / 2,
                                    ),
                                    itemCount: maxAge - minAge + 1,
                                    itemBuilder: (context, index) {
                                      final age = minAge + index;
                                      final isSelected = age == _selectedAge;
                                      
                                      return GestureDetector(
                                        onTap: () {
                                          _selectAge(age);
                                          _scrollController.animateTo(
                                            index * itemHeight,
                                            duration: const Duration(milliseconds: 300),
                                            curve: Curves.easeOut,
                                          );
                                        },
                                        child: Container(
                                          height: itemHeight,
                                          alignment: Alignment.center,
                                          child: AnimatedDefaultTextStyle(
                                            duration: const Duration(milliseconds: 200),
                                            style: theme.textTheme.headlineLarge?.copyWith(
                                              color: isSelected 
                                                  ? Colors.white 
                                                  : Colors.white.withValues(alpha: 0.4),
                                              fontWeight: isSelected 
                                                  ? FontWeight.w700 
                                                  : FontWeight.w400,
                                              fontSize: isSelected ? 36 : 24,
                                            ) ?? const TextStyle(),
                                            child: Text('$age'),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Next Button
                  OnboardingButton(
                    text: 'Continue',
                    onPressed: _selectedAge != null ? widget.onNext : null,
                    isEnabled: _selectedAge != null,
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}