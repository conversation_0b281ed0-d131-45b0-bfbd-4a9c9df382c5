import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/providers/onboarding_progress_provider.dart';
import 'package:openfit/widgets/onboarding/modern_widgets.dart';

class ModernPersonalInfoScreen extends ConsumerStatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onNext;

  const ModernPersonalInfoScreen({
    super.key,
    this.onBack,
    this.onNext,
  });

  @override
  ConsumerState<ModernPersonalInfoScreen> createState() => _ModernPersonalInfoScreenState();
}

class _ModernPersonalInfoScreenState extends ConsumerState<ModernPersonalInfoScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _fullName = '';
  String _selectedGender = '';
  double _age = 25;
  double _height = 170;
  double _weight = 70;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
    _loadExistingData();
  }

  void _loadExistingData() {
    final progressNotifier = ref.read(onboardingProgressProvider.notifier);
    _fullName = progressNotifier.getStepData<String>('fullName') ?? '';
    _selectedGender = progressNotifier.getStepData<String>('gender') ?? '';
    _age = progressNotifier.getStepData<double>('age') ?? 25;
    _height = progressNotifier.getStepData<double>('height') ?? 170;
    _weight = progressNotifier.getStepData<double>('weight') ?? 70;
    setState(() {});
  }

  void _saveData() {
    final progressNotifier = ref.read(onboardingProgressProvider.notifier);
    progressNotifier.updateMultipleStepData({
      'fullName': _fullName,
      'gender': _selectedGender,
      'age': _age,
      'height': _height,
      'weight': _weight,
    });
  }

  bool get _canProceed {
    return _fullName.isNotEmpty && _selectedGender.isNotEmpty;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D2B),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  const Text(
                    'Tell us about yourself',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This helps us create a personalized experience for you',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 16,
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Form Content
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          // Full Name Input
                          ModernTextInput(
                            label: 'Full Name',
                            hint: 'Enter your full name',
                            initialValue: _fullName,
                            onChanged: (value) {
                              setState(() {
                                _fullName = value;
                              });
                              _saveData();
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your name';
                              }
                              return null;
                            },
                            prefixIcon: const Icon(
                              Icons.person_outline,
                              color: Colors.white54,
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Gender Selection
                          const Text(
                            'Gender',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          Row(
                            children: [
                              Expanded(
                                child: ModernChoiceCard(
                                  title: 'Male',
                                  icon: Icons.male,
                                  isSelected: _selectedGender == 'Male',
                                  onTap: () {
                                    setState(() {
                                      _selectedGender = 'Male';
                                    });
                                    _saveData();
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ModernChoiceCard(
                                  title: 'Female',
                                  icon: Icons.female,
                                  isSelected: _selectedGender == 'Female',
                                  onTap: () {
                                    setState(() {
                                      _selectedGender = 'Female';
                                    });
                                    _saveData();
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: ModernChoiceCard(
                                  title: 'Other',
                                  icon: Icons.person,
                                  isSelected: _selectedGender == 'Other',
                                  onTap: () {
                                    setState(() {
                                      _selectedGender = 'Other';
                                    });
                                    _saveData();
                                  },
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Age Slider
                          ModernSlider(
                            label: 'Age',
                            value: _age,
                            min: 16,
                            max: 80,
                            unit: ' years',
                            onChanged: (value) {
                              setState(() {
                                _age = value;
                              });
                              _saveData();
                            },
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Height Slider
                          ModernSlider(
                            label: 'Height',
                            value: _height,
                            min: 140,
                            max: 220,
                            unit: ' cm',
                            onChanged: (value) {
                              setState(() {
                                _height = value;
                              });
                              _saveData();
                            },
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Weight Slider
                          ModernSlider(
                            label: 'Weight',
                            value: _weight,
                            min: 40,
                            max: 150,
                            unit: ' kg',
                            onChanged: (value) {
                              setState(() {
                                _weight = value;
                              });
                              _saveData();
                            },
                          ),
                          
                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),
                  
                  // Navigation Buttons
                  Row(
                    children: [
                      if (widget.onBack != null) ...[
                        Expanded(
                          child: Container(
                            height: 56,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.2),
                              ),
                            ),
                            child: TextButton(
                              onPressed: widget.onBack,
                              child: const Text(
                                'Back',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      
                      Expanded(
                        flex: 2,
                        child: Container(
                          height: 56,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: _canProceed
                                ? const LinearGradient(
                                    colors: [Color(0xFFFF6B47), Color(0xFFFF8A65)],
                                  )
                                : null,
                            color: _canProceed ? null : Colors.grey.withValues(alpha: 0.3),
                          ),
                          child: TextButton(
                            onPressed: _canProceed ? widget.onNext : null,
                            child: const Text(
                              'Continue',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}