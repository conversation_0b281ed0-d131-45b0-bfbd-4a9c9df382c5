import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';
import 'package:openfit/widgets/onboarding/multi_select_cards.dart';
import 'package:openfit/widgets/onboarding/drag_reorder_list.dart';
import 'package:openfit/widgets/onboarding/text_input_field.dart';

class FitnessGoalsScreen extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback? onBack;
  final VoidCallback? onNext;
  final VoidCallback? onSkip;
  final ValueChanged<OnboardingData> onDataChanged;

  const FitnessGoalsScreen({
    super.key,
    required this.data,
    required this.onDataChanged,
    this.onBack,
    this.onNext,
    this.onSkip,
  });

  @override
  State<FitnessGoalsScreen> createState() => _FitnessGoalsScreenState();
}

class _FitnessGoalsScreenState extends State<FitnessGoalsScreen> {
  late List<FitnessGoal> _fitnessGoals;
  late List<FitnessGoal> _prioritizedGoals;
  late String? _additionalGoalInfo;

  @override
  void initState() {
    super.initState();
    _fitnessGoals = List.from(widget.data.fitnessGoals);
    _prioritizedGoals = List.from(widget.data.prioritizedGoals);
    _additionalGoalInfo = widget.data.additionalGoalInfo;
  }

  void _updateData() {
    final updatedData = widget.data.copyWith(
      fitnessGoals: _fitnessGoals,
      prioritizedGoals: _prioritizedGoals,
      additionalGoalInfo: _additionalGoalInfo,
    );
    widget.onDataChanged(updatedData);
  }

  void _onGoalsSelected(List<FitnessGoal> goals) {
    setState(() {
      _fitnessGoals = goals;
      // Update prioritized goals to only include selected goals
      _prioritizedGoals = _prioritizedGoals.where((goal) => goals.contains(goal)).toList();
    });
    _updateData();
  }

  void _onGoalsReordered(List<FitnessGoal> reorderedGoals) {
    setState(() {
      _prioritizedGoals = reorderedGoals;
    });
    _updateData();
  }

  bool get _canProceed {
    return _fitnessGoals.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            OnboardingHeader(
              title: "What Are Your Fitness Goals?",
              subtitle: "Help us create the perfect workout plan for you",
              onBack: widget.onBack,
              onSkip: widget.onSkip,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Multi-select goals
                    MultiSelectCards<FitnessGoal>(
                      title: "Select all that apply",
                      subtitle: "Choose your primary fitness goals",
                      items: FitnessGoal.values,
                      selectedItems: _fitnessGoals,
                      getTitle: (goal) => goal.displayName,
                      getDescription: (goal) => goal.description,
                      onSelectionChanged: _onGoalsSelected,
                    ),
                    
                    if (_fitnessGoals.isNotEmpty) ...[
                      const SizedBox(height: 32),
                      
                      // Priority ordering
                      DragReorderList<FitnessGoal>(
                        title: "Sort them in your preferred order",
                        subtitle: "Drag to reorder your goals by priority",
                        items: _fitnessGoals.isEmpty ? [] : 
                               _prioritizedGoals.isEmpty ? _fitnessGoals : _prioritizedGoals,
                        getTitle: (goal) => goal.displayName,
                        onReorder: _onGoalsReordered,
                      ),
                    ],
                    
                    const SizedBox(height: 32),
                    
                    // Additional information
                    TextInputField(
                      label: "Is there anything else I should know as your personal health coach?",
                      value: _additionalGoalInfo,
                      onChanged: (value) {
                        setState(() {
                          _additionalGoalInfo = value.isEmpty ? null : value;
                        });
                        _updateData();
                      },
                      hint: "Tell us about any specific needs, preferences, or limitations...",
                      maxLines: 4,
                    ),
                    
                    SizedBox(height: screenHeight * 0.15),
                  ],
                ),
              ),
            ),
            
            // Bottom Navigation
            Container(
              padding: const EdgeInsets.all(24),
              child: OnboardingButton(
                text: "Continue",
                onPressed: _canProceed ? widget.onNext : null,
                isEnabled: _canProceed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}