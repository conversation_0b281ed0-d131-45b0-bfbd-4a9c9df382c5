import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/models/user_profile.dart';
import 'package:openfit/providers/enhanced_auth_providers.dart';
import 'package:openfit/providers/onboarding_progress_provider.dart';
import 'package:openfit/screens/onboarding/introduction_screen.dart';
import 'package:openfit/screens/onboarding/modern_personal_info_screen.dart';
import 'package:openfit/screens/onboarding/fitness_goals_screen.dart';
import 'package:openfit/screens/onboarding/fitness_levels_screen.dart';
import 'package:openfit/screens/onboarding/equipment_selection_screen.dart';
import 'package:openfit/screens/onboarding/workout_schedule_screen.dart';
import 'package:openfit/screens/onboarding/additional_info_screen.dart';
import 'package:openfit/screens/main_screen.dart';
import 'package:openfit/widgets/onboarding/modern_widgets.dart';

class OnboardingFlow extends ConsumerStatefulWidget {
  const OnboardingFlow({super.key});

  @override
  ConsumerState<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends ConsumerState<OnboardingFlow>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _transitionController;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  OnboardingData _onboardingData = const OnboardingData();
  bool _hasLoadedUserData = false;

  static const int totalPages = 7;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    _transitionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOut,
    ));

    _transitionController.forward();
    
    // Load existing user data after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingUserData();
    });
  }

  void _loadExistingUserData() {
    if (_hasLoadedUserData) return;
    
    final userProfile = ref.read(userProfileProvider);
    userProfile.when(
      data: (profile) {
        if (profile != null) {
          setState(() {
            _onboardingData = _onboardingData.copyWith(
              fullName: profile.fullName,
              age: profile.age,
              gender: profile.gender != null 
                ? Gender.values.firstWhere(
                    (g) => g.displayName.toLowerCase() == profile.gender!.toLowerCase(),
                    orElse: () => Gender.male,
                  )
                : null,
              height: profile.heightCm?.toDouble(),
              heightUnit: HeightUnit.cm,
              weight: profile.weightKg?.toDouble(),
              weightUnit: WeightUnit.kg,
            );
            _hasLoadedUserData = true;
          });
        }
      },
      loading: () {},
      error: (_, __) {},
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _transitionController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < totalPages - 1) {
      setState(() {
        _currentPage++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _animatePageTransition();
      
      // Update progress provider
      ref.read(onboardingProgressProvider.notifier).nextStep();
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _animatePageTransition();
      
      // Update progress provider
      ref.read(onboardingProgressProvider.notifier).previousStep();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  void _completeOnboarding() async {
    try {
      // Save onboarding data to user profile
      final user = ref.read(currentUserProvider);
      if (user != null) {
        final profileNotifier = ref.read(profileNotifierProvider.notifier);
        
        // Create profile from onboarding data
        final profile = UserProfile(
          id: user.id,
          email: user.email,
          fullName: _onboardingData.fullName,
          age: _onboardingData.age,
          gender: _onboardingData.gender?.displayName,
          heightCm: _onboardingData.height?.toInt(),
          weightKg: _onboardingData.weight,
          fitnessLevel: _mapFitnessLevel(_onboardingData.cardioLevel, _onboardingData.weightLiftingLevel),
          goals: _onboardingData.fitnessGoals.map((g) => g.name).toList(),
          preferredWorkoutDays: _onboardingData.preferredDays.map((d) => d.displayName.toLowerCase()).toList(),
          preferredWorkoutTime: _mapWorkoutTime(_onboardingData.workoutDuration),
          onboardingCompleted: true,
          hasCompletedPreferences: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await profileNotifier.updateProfile(profile);
      }
      
      // Navigate to main screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const MainScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    } catch (e) {
      // Handle error - show snackbar or dialog
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving profile: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _animatePageTransition() {
    _transitionController.reset();
    _transitionController.forward();
  }

  void _updateOnboardingData(OnboardingData newData) {
    setState(() {
      _onboardingData = newData;
    });
  }

  /// Map fitness levels to a string representation
  String _mapFitnessLevel(double? cardioLevel, double? weightLiftingLevel) {
    final avgLevel = ((cardioLevel ?? 0) + (weightLiftingLevel ?? 0)) / 2;
    if (avgLevel <= 1.0) return 'beginner';
    if (avgLevel <= 2.5) return 'intermediate';
    return 'advanced';
  }

  /// Map workout duration to preferred time
  String? _mapWorkoutTime(WorkoutDuration? duration) {
    if (duration == null) return null;
    // This is a simple mapping - you might want to make it more sophisticated
    return 'morning'; // Default to morning for now
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressState = ref.watch(onboardingProgressProvider);
    final stepTitles = ref.watch(onboardingStepTitlesProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF1A1D2B),
      body: CelebrationTrigger(
        shouldCelebrate: progressState.isCompleted,
        celebrationMessage: 'Welcome to OpenFit!',
        celebrationSubtitle: 'Your fitness journey starts now',
        onCelebrationComplete: () {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const MainScreen()),
          );
        },
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1D2B),
                    Color(0xFF2A2D3A),
                  ],
                ),
              ),
            ),
            
            // Progress bar at top
            SafeArea(
              child: OnboardingProgress(
                currentStep: _currentPage + 1,
                totalSteps: totalPages,
                stepTitles: stepTitles.take(totalPages).toList(),
                showPercentage: true,
                showStepNumbers: false,
                padding: const EdgeInsets.all(20),
              ),
            ),
            
            // Main Content
            Padding(
              padding: const EdgeInsets.only(top: 120),
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
              // Introduction Screen
              SlideTransition(
                position: _slideAnimation,
                child: IntroductionScreen(
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),

              // Personal Information Screen
              SlideTransition(
                position: _slideAnimation,
                child: ModernPersonalInfoScreen(
                  onBack: _previousPage,
                  onNext: _nextPage,
                ),
              ),

              // Fitness Goals Screen
              SlideTransition(
                position: _slideAnimation,
                child: FitnessGoalsScreen(
                  data: _onboardingData,
                  onDataChanged: _updateOnboardingData,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),

              // Fitness Levels Screen
              SlideTransition(
                position: _slideAnimation,
                child: FitnessLevelsScreen(
                  data: _onboardingData,
                  onDataChanged: _updateOnboardingData,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),

              // Equipment Selection Screen
              SlideTransition(
                position: _slideAnimation,
                child: EquipmentSelectionScreen(
                  data: _onboardingData,
                  onDataChanged: _updateOnboardingData,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),

              // Workout Schedule Screen
              SlideTransition(
                position: _slideAnimation,
                child: WorkoutScheduleScreen(
                  data: _onboardingData,
                  onDataChanged: _updateOnboardingData,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),

              // Additional Information Screen
              SlideTransition(
                position: _slideAnimation,
                child: AdditionalInfoScreen(
                  data: _onboardingData,
                  onDataChanged: _updateOnboardingData,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onSkip: _skipOnboarding,
                ),
              ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}