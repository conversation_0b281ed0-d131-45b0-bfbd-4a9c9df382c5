import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';
import 'package:openfit/widgets/onboarding/slider_input.dart';
import 'package:openfit/widgets/onboarding/text_input_field.dart';

class FitnessLevelsScreen extends StatefulWidget {
  final OnboardingData data;
  final VoidCallback? onBack;
  final VoidCallback? onNext;
  final VoidCallback? onSkip;
  final ValueChanged<OnboardingData> onDataChanged;

  const FitnessLevelsScreen({
    super.key,
    required this.data,
    required this.onDataChanged,
    this.onBack,
    this.onNext,
    this.onSkip,
  });

  @override
  State<FitnessLevelsScreen> createState() => _FitnessLevelsScreenState();
}

class _FitnessLevelsScreenState extends State<FitnessLevelsScreen> {
  late double _cardioLevel;
  late double _weightLiftingLevel;
  late String? _additionalFitnessInfo;

  static const List<String> fitnessLevels = [
    'Beginner', 'Low', 'Moderate', 'High', 'Elite'
  ];

  @override
  void initState() {
    super.initState();
    _cardioLevel = widget.data.cardioLevel ?? 0.0;
    _weightLiftingLevel = widget.data.weightLiftingLevel ?? 0.0;
    _additionalFitnessInfo = widget.data.additionalFitnessInfo;
  }

  void _updateData() {
    final updatedData = widget.data.copyWith(
      cardioLevel: _cardioLevel,
      weightLiftingLevel: _weightLiftingLevel,
      additionalFitnessInfo: _additionalFitnessInfo,
    );
    widget.onDataChanged(updatedData);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            OnboardingHeader(
              title: "Current Fitness Levels",
              subtitle: "Help us understand your current fitness abilities",
              onBack: widget.onBack,
              onSkip: widget.onSkip,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section Title
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        "Rate Your Current Fitness Levels",
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Cardio Level Slider
                    SliderInput(
                      title: "Cardio Level",
                      subtitle: "How would you rate your current cardiovascular fitness?",
                      value: _cardioLevel,
                      min: 0,
                      max: 4,
                      divisions: 4,
                      labels: fitnessLevels,
                      onChanged: (value) {
                        setState(() {
                          _cardioLevel = value;
                        });
                        _updateData();
                      },
                    ),
                    const SizedBox(height: 32),

                    // Weight-lifting Level Slider  
                    SliderInput(
                      title: "Weight-lifting Level",
                      subtitle: "How would you rate your current strength training experience?",
                      value: _weightLiftingLevel,
                      min: 0,
                      max: 4,
                      divisions: 4,
                      labels: fitnessLevels,
                      onChanged: (value) {
                        setState(() {
                          _weightLiftingLevel = value;
                        });
                        _updateData();
                      },
                    ),
                    const SizedBox(height: 32),
                    
                    // Additional comments
                    TextInputField(
                      label: "Additional comments about your fitness",
                      value: _additionalFitnessInfo,
                      onChanged: (value) {
                        setState(() {
                          _additionalFitnessInfo = value.isEmpty ? null : value;
                        });
                        _updateData();
                      },
                      hint: "Tell us about your fitness background, any injuries, or specific considerations...",
                      maxLines: 4,
                    ),
                    
                    SizedBox(height: screenHeight * 0.15),
                  ],
                ),
              ),
            ),
            
            // Bottom Navigation
            Container(
              padding: const EdgeInsets.all(24),
              child: OnboardingButton(
                text: "Continue",
                onPressed: widget.onNext,
                isEnabled: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}