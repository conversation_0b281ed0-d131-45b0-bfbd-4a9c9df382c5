import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';

class GoalsSelectionScreen extends StatefulWidget {
  final List<FitnessGoal> selectedGoals;
  final Function(List<FitnessGoal>) onGoalsSelected;
  final VoidCallback onBack;
  final VoidCallback onNext;
  final VoidCallback onSkip;

  const GoalsSelectionScreen({
    super.key,
    required this.selectedGoals,
    required this.onGoalsSelected,
    required this.onBack,
    required this.onNext,
    required this.onSkip,
  });

  @override
  State<GoalsSelectionScreen> createState() => _GoalsSelectionScreenState();
}

class _GoalsSelectionScreenState extends State<GoalsSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<FitnessGoal> _selectedGoals = [];

  @override
  void initState() {
    super.initState();
    _selectedGoals = List.from(widget.selectedGoals);
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleGoal(FitnessGoal goal) {
    setState(() {
      if (_selectedGoals.contains(goal)) {
        _selectedGoals.remove(goal);
      } else {
        _selectedGoals.add(goal);
      }
    });
    widget.onGoalsSelected(_selectedGoals);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header
          OnboardingHeader(
            title: "What's your goal?",
            subtitle: 'This help us to create your personalized exercise plan.',
            onBack: widget.onBack,
            onSkip: widget.onSkip,
          ),

          // Main Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // Goals Grid
                  Expanded(
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              // Row 1: Cardio, Lose Weight
                              Row(
                                children: [
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.increaseStamina,
                                      isSelected: _selectedGoals.contains(FitnessGoal.increaseStamina),
                                      onTap: () => _toggleGoal(FitnessGoal.increaseStamina),
                                      primaryColor: theme.colorScheme.primary,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.loseWeight,
                                      isSelected: _selectedGoals.contains(FitnessGoal.loseWeight),
                                      onTap: () => _toggleGoal(FitnessGoal.loseWeight),
                                      primaryColor: Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Row 2: Strength
                              Row(
                                children: [
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.increaseStrength,
                                      isSelected: _selectedGoals.contains(FitnessGoal.increaseStrength),
                                      onTap: () => _toggleGoal(FitnessGoal.increaseStrength),
                                      primaryColor: Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(child: Container()), // Empty space
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Row 3: Reduce Stress
                              Row(
                                children: [
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.reduceStress,
                                      isSelected: _selectedGoals.contains(FitnessGoal.reduceStress),
                                      onTap: () => _toggleGoal(FitnessGoal.reduceStress),
                                      primaryColor: theme.colorScheme.secondary,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(child: Container()), // Empty space
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Row 4: Flexibility, Sports Activities
                              Row(
                                children: [
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.improveFlexibility,
                                      isSelected: _selectedGoals.contains(FitnessGoal.improveFlexibility),
                                      onTap: () => _toggleGoal(FitnessGoal.improveFlexibility),
                                      primaryColor: Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.trainingForSport,
                                      isSelected: _selectedGoals.contains(FitnessGoal.trainingForSport),
                                      onTap: () => _toggleGoal(FitnessGoal.trainingForSport),
                                      primaryColor: Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Row 5: Stay Fit, Nutrition Gain
                              Row(
                                children: [
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.generalFitness,
                                      isSelected: _selectedGoals.contains(FitnessGoal.generalFitness),
                                      onTap: () => _toggleGoal(FitnessGoal.generalFitness),
                                      primaryColor: const Color(0xFFFFD700), // Gold color
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: GoalOption(
                                      goal: FitnessGoal.loseWeight,
                                      isSelected: _selectedGoals.contains(FitnessGoal.loseWeight),
                                      onTap: () => _toggleGoal(FitnessGoal.loseWeight),
                                      primaryColor: Colors.grey.withValues(alpha: 0.3),
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 40),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Next Button
                  OnboardingButton(
                    text: 'Continue',
                    onPressed: _selectedGoals.isNotEmpty ? widget.onNext : null,
                    isEnabled: _selectedGoals.isNotEmpty,
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class GoalOption extends StatefulWidget {
  final FitnessGoal goal;
  final bool isSelected;
  final VoidCallback onTap;
  final Color primaryColor;

  const GoalOption({
    super.key,
    required this.goal,
    required this.isSelected,
    required this.onTap,
    required this.primaryColor,
  });

  @override
  State<GoalOption> createState() => _GoalOptionState();
}

class _GoalOptionState extends State<GoalOption>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: widget.onTap,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: 120,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.primaryColor.withValues(alpha: 0.8),
                      widget.primaryColor.withValues(alpha: 0.6),
                    ],
                  )
                : null,
            color: widget.isSelected
                ? null
                : Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: widget.isSelected
                  ? theme.colorScheme.primary
                  : Colors.white.withValues(alpha: 0.3),
              width: widget.isSelected ? 2 : 1,
            ),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: widget.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ]
                : [],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Checkmark
              Align(
                alignment: Alignment.topRight,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 200),
                  opacity: widget.isSelected ? 1.0 : 0.0,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              
              // Goal Text
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.goal.displayName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}