import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/providers/enhanced_auth_providers.dart';
import 'package:openfit/config/supabase_config.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';
import 'package:openfit/widgets/onboarding/text_input_field.dart';
import 'package:openfit/widgets/onboarding/unit_selector.dart';

class PersonalInfoScreen extends ConsumerStatefulWidget {
  final OnboardingData data;
  final VoidCallback? onBack;
  final VoidCallback? onNext;
  final VoidCallback? onSkip;
  final ValueChanged<OnboardingData> onDataChanged;

  const PersonalInfoScreen({
    super.key,
    required this.data,
    required this.onDataChanged,
    this.onBack,
    this.onNext,
    this.onSkip,
  });

  @override
  ConsumerState<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends ConsumerState<PersonalInfoScreen> {
  late String? _fullName;
  late Gender? _gender;
  late int? _age;
  late double? _height;
  late HeightUnit? _heightUnit;
  late double? _weight;
  late WeightUnit? _weightUnit;
  bool _hasLoadedUserData = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // Start with onboarding data
    _fullName = widget.data.fullName;
    _gender = widget.data.gender;
    _age = widget.data.age;
    _height = widget.data.height;
    _heightUnit = widget.data.heightUnit ?? HeightUnit.feet;
    _weight = widget.data.weight;
    _weightUnit = widget.data.weightUnit ?? WeightUnit.lbs;
  }



  void _updateData() {
    final updatedData = widget.data.copyWith(
      fullName: _fullName,
      gender: _gender,
      age: _age,
      height: _height,
      heightUnit: _heightUnit,
      weight: _weight,
      weightUnit: _weightUnit,
    );
    widget.onDataChanged(updatedData);
  }

  bool get _canProceed {
    return _fullName?.isNotEmpty == true && _gender != null && _age != null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    
    // Watch user profile and load data when available
    final userProfile = ref.watch(userProfileProvider);
    final currentUser = ref.watch(currentUserProvider);
    
    // Debug: Print user and profile data
    print('Current user: ${currentUser?.id}');
    userProfile.when(
      data: (profile) {
        print('Profile data: ${profile?.toJson()}');
        if (profile != null && !_hasLoadedUserData) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              // Pre-populate with existing user data if fields are empty or null
              if ((_fullName?.isEmpty ?? true) && profile.fullName != null) {
                _fullName = profile.fullName;
              }
              
              if (_age == null && profile.age != null) {
                _age = profile.age;
              }
              
              // Map gender from string to enum
              if (_gender == null && profile.gender != null) {
                try {
                  _gender = Gender.values.firstWhere(
                    (g) => g.displayName.toLowerCase() == profile.gender!.toLowerCase(),
                    orElse: () => Gender.male,
                  );
                } catch (e) {
                  print('Error mapping gender: $e');
                }
              }
              
              // Convert height - the database stores height as numeric, not necessarily in cm
              if (_height == null && profile.heightCm != null) {
                _height = profile.heightCm!.toDouble();
                _heightUnit = HeightUnit.cm; // Assume cm since that's what the model expects
              }
              
              // Set weight
              if (_weight == null && profile.weightKg != null) {
                _weight = profile.weightKg;
                _weightUnit = WeightUnit.kg; // Assume kg since that's what the model expects
              }
              
              _hasLoadedUserData = true;
            });
            _updateData();
          });
        }
      },
      loading: () {
        print('Profile loading...');
      },
      error: (error, stack) {
        print('Profile error: $error');
      },
    );
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            OnboardingHeader(
              title: "Tell Us About Yourself",
              subtitle: "Help us personalize your fitness journey",
              onBack: widget.onBack,
              onSkip: widget.onSkip,
            ),
            
            // Development only - Quick exit for testing
            if (SupabaseConfig.isDevelopment)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                child: TextButton.icon(
                  onPressed: () async {
                    // Complete onboarding to get back to main app
                    await ref.read(profileNotifierProvider.notifier).completeOnboarding();
                  },
                  icon: const Icon(Icons.exit_to_app, size: 16),
                  label: const Text('Skip to Main App (Dev Only)', style: TextStyle(fontSize: 12)),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.orange,
                    backgroundColor: Colors.orange.withOpacity(0.1),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
              ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section Title
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        "Personal Information",
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Full Name
                    TextInputField(
                      label: "Full Name",
                      value: _fullName,
                      onChanged: (value) {
                        setState(() {
                          _fullName = value.isEmpty ? null : value;
                        });
                        _updateData();
                      },
                      hint: "Enter your full name",
                      isRequired: true,
                    ),
                    const SizedBox(height: 24),

                    // Gender Selection
                    DropdownSelector<Gender>(
                      label: "Gender",
                      value: _gender,
                      items: Gender.values,
                      getLabel: (gender) => gender.displayName,
                      onChanged: (gender) {
                        setState(() {
                          _gender = gender;
                        });
                        _updateData();
                      },
                      hint: "Select your gender",
                      isRequired: true,
                    ),
                    const SizedBox(height: 24),

                    // Age
                    NumberInput(
                      label: "Age",
                      value: _age,
                      onChanged: (age) {
                        setState(() {
                          _age = age;
                        });
                        _updateData();
                      },
                      hint: "Enter your age",
                      isRequired: true,
                      min: 13,
                      max: 120,
                    ),
                    const SizedBox(height: 24),

                    // Height
                    UnitSelector<HeightUnit>(
                      label: "Height",
                      value: _height,
                      selectedUnit: _heightUnit,
                      units: HeightUnit.values,
                      getUnitLabel: (unit) => unit.displayName,
                      onValueChanged: (height) {
                        setState(() {
                          _height = height;
                        });
                        _updateData();
                      },
                      onUnitChanged: (unit) {
                        setState(() {
                          _heightUnit = unit;
                        });
                        _updateData();
                      },
                      hint: "Enter height",
                    ),
                    const SizedBox(height: 24),

                    // Weight
                    UnitSelector<WeightUnit>(
                      label: "Weight",
                      value: _weight,
                      selectedUnit: _weightUnit,
                      units: WeightUnit.values,
                      getUnitLabel: (unit) => unit.displayName,
                      onValueChanged: (weight) {
                        setState(() {
                          _weight = weight;
                        });
                        _updateData();
                      },
                      onUnitChanged: (unit) {
                        setState(() {
                          _weightUnit = unit;
                        });
                        _updateData();
                      },
                      hint: "Enter weight",
                    ),
                    
                    SizedBox(height: screenHeight * 0.15),
                  ],
                ),
              ),
            ),
            
            // Bottom Navigation
            Container(
              padding: const EdgeInsets.all(24),
              child: OnboardingButton(
                text: "Continue",
                onPressed: _canProceed ? widget.onNext : null,
                isEnabled: _canProceed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}