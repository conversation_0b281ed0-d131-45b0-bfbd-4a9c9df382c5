import 'package:flutter/material.dart';
import 'package:openfit/models/onboarding_data.dart';
import 'package:openfit/widgets/onboarding/onboarding_header.dart';
import 'package:openfit/widgets/onboarding/onboarding_button.dart';

class GenderSelectionScreen extends StatefulWidget {
  final Gender? selectedGender;
  final Function(Gender) onGenderSelected;
  final VoidCallback onBack;
  final VoidCallback onNext;
  final VoidCallback onSkip;

  const GenderSelectionScreen({
    super.key,
    this.selectedGender,
    required this.onGenderSelected,
    required this.onBack,
    required this.onNext,
    required this.onSkip,
  });

  @override
  State<GenderSelectionScreen> createState() => _GenderSelectionScreenState();
}

class _GenderSelectionScreenState extends State<GenderSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  Gender? _selectedGender;

  @override
  void initState() {
    super.initState();
    _selectedGender = widget.selectedGender;
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _selectGender(Gender gender) {
    setState(() {
      _selectedGender = gender;
    });
    widget.onGenderSelected(gender);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header
          OnboardingHeader(
            title: 'Tell us about yourself!',
            subtitle: 'To give you a better experience we need to know your gender.',
            onBack: widget.onBack,
            onSkip: widget.onSkip,
          ),

          // Main Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 60),

                  // Gender Options
                  Expanded(
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Male Option
                            GenderOption(
                              gender: Gender.male,
                              isSelected: _selectedGender == Gender.male,
                              onTap: () => _selectGender(Gender.male),
                              icon: Icons.male,
                              label: 'Male',
                              gradientColors: [
                                const Color(0xFF6B4E3D).withValues(alpha: 0.8),
                                const Color(0xFF8B6F47).withValues(alpha: 0.9),
                              ],
                            ),

                            const SizedBox(height: 32),

                            // Female Option
                            GenderOption(
                              gender: Gender.female,
                              isSelected: _selectedGender == Gender.female,
                              onTap: () => _selectGender(Gender.female),
                              icon: Icons.female,
                              label: 'Female',
                              gradientColors: [
                                const Color(0xFF2A2D3A).withValues(alpha: 0.8),
                                const Color(0xFF3D4A3D).withValues(alpha: 0.9),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Next Button
                  OnboardingButton(
                    text: 'Continue',
                    onPressed: _selectedGender != null ? widget.onNext : null,
                    isEnabled: _selectedGender != null,
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class GenderOption extends StatefulWidget {
  final Gender gender;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData icon;
  final String label;
  final List<Color> gradientColors;

  const GenderOption({
    super.key,
    required this.gender,
    required this.isSelected,
    required this.onTap,
    required this.icon,
    required this.label,
    required this.gradientColors,
  });

  @override
  State<GenderOption> createState() => _GenderOptionState();
}

class _GenderOptionState extends State<GenderOption>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: widget.onTap,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: 180,
          height: 180,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: widget.isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: widget.gradientColors,
                  )
                : null,
            color: widget.isSelected
                ? null
                : Colors.white.withValues(alpha: 0.1),
            border: Border.all(
              color: widget.isSelected
                  ? theme.colorScheme.primary
                  : Colors.white.withValues(alpha: 0.3),
              width: widget.isSelected ? 3 : 1,
            ),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ]
                : [],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.icon,
                  size: 32,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Label
              Text(
                widget.label,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}