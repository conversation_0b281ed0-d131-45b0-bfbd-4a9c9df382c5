import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/onboarding/onboarding_progress.dart';
import '../widgets/onboarding/celebration_animation.dart';
import '../providers/onboarding_progress_provider.dart';

class ProgressTrackingDemo extends ConsumerStatefulWidget {
  const ProgressTrackingDemo({super.key});

  @override
  ConsumerState<ProgressTrackingDemo> createState() => _ProgressTrackingDemoState();
}

class _ProgressTrackingDemoState extends ConsumerState<ProgressTrackingDemo> {
  bool _showCelebration = false;

  @override
  Widget build(BuildContext context) {
    final progressState = ref.watch(onboardingProgressProvider);
    final stepTitles = ref.watch(onboardingStepTitlesProvider);
    
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D2B),
      appBar: AppBar(
        title: const Text('Progress Tracking Demo'),
        backgroundColor: const Color(0xFF2A2D3A),
        foregroundColor: Colors.white,
      ),
      body: CelebrationTrigger(
        shouldCelebrate: _showCelebration,
        celebrationMessage: 'Amazing Progress!',
        celebrationSubtitle: 'You\'ve completed another step',
        onCelebrationComplete: () {
          setState(() {
            _showCelebration = false;
          });
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress Widget
              _buildSection(
                'Onboarding Progress',
                OnboardingProgress(
                  currentStep: progressState.currentStep.stepIndex + 1,
                  totalSteps: OnboardingStep.totalSteps,
                  stepTitles: stepTitles,
                  showPercentage: true,
                  showStepNumbers: true,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Progress Info
              _buildSection(
                'Progress Information',
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: const Color(0xFF2A2D3A).withValues(alpha: 0.6),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow('Current Step', progressState.currentStep.title),
                      _buildInfoRow('Step Index', '${progressState.currentStep.stepIndex}'),
                      _buildInfoRow('Progress', '${progressState.progressPercentage.round()}%'),
                      _buildInfoRow('Is Completed', progressState.isCompleted.toString()),
                      _buildInfoRow('Last Saved', progressState.lastSaved?.toString() ?? 'Never'),
                      if (progressState.error != null)
                        _buildInfoRow('Error', progressState.error!, isError: true),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Step Data
              _buildSection(
                'Step Data',
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: const Color(0xFF2A2D3A).withValues(alpha: 0.6),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: progressState.stepData.isEmpty
                      ? const Text(
                          'No step data saved yet',
                          style: TextStyle(color: Colors.white70),
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: progressState.stepData.entries.map((entry) {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: Text(
                                      '${entry.key}:',
                                      style: const TextStyle(
                                        color: Colors.white70,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      entry.value.toString(),
                                      style: const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Navigation Controls
              _buildSection(
                'Navigation Controls',
                Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: progressState.currentStep.stepIndex > 0
                                ? () => ref.read(onboardingProgressProvider.notifier).previousStep()
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2A2D3A),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Previous Step'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: !progressState.isCompleted
                                ? () {
                                    ref.read(onboardingProgressProvider.notifier).nextStep();
                                    setState(() {
                                      _showCelebration = true;
                                    });
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFFF6B47),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Next Step'),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => ref.read(onboardingProgressProvider.notifier).resetProgress(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red.withValues(alpha: 0.2),
                              foregroundColor: Colors.red,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Reset Progress'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => ref.read(onboardingProgressProvider.notifier).completeOnboarding(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.withValues(alpha: 0.2),
                              foregroundColor: Colors.green,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Complete'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Sample Data Controls
              _buildSection(
                'Sample Data Controls',
                Column(
                  children: [
                    ElevatedButton(
                      onPressed: () => _addSampleData(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2A2D3A),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        minimumSize: const Size(double.infinity, 0),
                      ),
                      child: const Text('Add Sample Step Data'),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    ElevatedButton(
                      onPressed: () => _triggerCelebration(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFF6B47),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        minimumSize: const Size(double.infinity, 0),
                      ),
                      child: const Text('Trigger Celebration'),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Step Selection
              _buildSection(
                'Jump to Step',
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: OnboardingStep.values
                      .where((step) => step != OnboardingStep.completed)
                      .map((step) {
                    final isCurrent = step == progressState.currentStep;
                    return GestureDetector(
                      onTap: () => ref.read(onboardingProgressProvider.notifier).goToStep(step),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: isCurrent 
                              ? const Color(0xFFFF6B47)
                              : const Color(0xFF2A2D3A).withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isCurrent 
                                ? const Color(0xFFFF6B47)
                                : Colors.white.withValues(alpha: 0.1),
                          ),
                        ),
                        child: Text(
                          '${step.stepIndex}: ${step.title}',
                          style: TextStyle(
                            color: isCurrent ? Colors.white : Colors.white70,
                            fontSize: 12,
                            fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isError = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white70,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isError ? Colors.red : Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addSampleData() {
    final notifier = ref.read(onboardingProgressProvider.notifier);
    final currentStep = ref.read(onboardingProgressProvider).currentStep;
    
    switch (currentStep) {
      case OnboardingStep.personalInfo:
        notifier.updateMultipleStepData({
          'fullName': 'John Doe',
          'age': 28,
          'gender': 'Male',
          'height': 175,
          'weight': 70,
        });
        break;
      case OnboardingStep.fitnessGoals:
        notifier.updateMultipleStepData({
          'fitnessGoals': ['Weight Loss', 'Muscle Gain'],
          'primaryGoal': 'Weight Loss',
        });
        break;
      case OnboardingStep.experienceLevel:
        notifier.updateMultipleStepData({
          'experienceLevel': 3,
          'previousActivities': ['Running', 'Gym'],
        });
        break;
      default:
        notifier.updateStepData('sampleData', 'Added at ${DateTime.now()}');
    }
  }

  void _triggerCelebration() {
    setState(() {
      _showCelebration = true;
    });
  }
}