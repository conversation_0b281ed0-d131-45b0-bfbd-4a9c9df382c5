import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:openfit/core/error_handler.dart';
import 'package:openfit/providers/app_state.dart';
import 'package:openfit/screens/auth/auth_wrapper.dart';
import 'package:openfit/services/supabase_service_simple.dart';
import 'package:openfit/theme.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Supabase
  await SupabaseService.initialize();
  
  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    ErrorHandler.handleError(
      details.exception,
      details.stack ?? StackTrace.current,
      context: 'Flutter Framework Error',
    );
  };

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appSettings = ref.watch(appSettingsProvider);
    
    return MaterialApp(
      title: 'OpenFit',
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: appSettings.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      home: ErrorBoundary(
        fallback: ErrorBoundary.defaultFallback(context, 'App initialization error'),
        child: const AuthWrapper(),
      ),
      builder: (context, child) {
        // Global error boundary
        ErrorWidget.builder = (FlutterErrorDetails details) {
          return ErrorBoundary.defaultFallback(context, details.exception);
        };
        return child!;
      },
    );
  }
}
